<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms') ?>">Farms</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/pesticides_data') ?>">Pesticides Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Details</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/pesticides_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/farms/pesticides_data/' . $pesticide_data['id'] . '/edit') ?>" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $pesticide_data['id'] ?>)">
                        <i class="fas fa-trash me-2"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="row">
        <!-- Pesticide Information -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>Pesticide Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Pesticide Name</label>
                            <div class="fw-bold"><?= esc($pesticide_data['name']) ?></div>
                            <?php if (!empty($pesticide_data['pesticide_name'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>Registered as: <?= esc($pesticide_data['pesticide_name']) ?>
                                </small>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Brand</label>
                            <div><?= esc($pesticide_data['brand'] ?: 'Not specified') ?></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Quantity</label>
                            <div class="fw-bold h5 mb-0 text-primary">
                                <?= esc($pesticide_data['quantity']) ?> <?= esc($pesticide_data['unit']) ?>
                            </div>
                            <small class="text-muted"><?= esc($pesticide_data['unit_of_measure']) ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Application Date</label>
                            <div class="fw-bold">
                                <?= date('M d, Y', strtotime($pesticide_data['action_date'])) ?>
                            </div>
                            <small class="text-muted"><?= date('l', strtotime($pesticide_data['action_date'])) ?></small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div>
                                <?php if ($pesticide_data['status'] === 'active'): ?>
                                    <span class="badge bg-success fs-6">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary fs-6"><?= ucfirst($pesticide_data['status']) ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($pesticide_data['remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <label class="form-label text-muted">Remarks</label>
                                <div class="bg-light p-3 rounded">
                                    <?= nl2br(esc($pesticide_data['remarks'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Farm Block Information -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Farm Block Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Block Code</label>
                        <div class="fw-bold h6"><?= esc($pesticide_data['block_code']) ?></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Block Site</label>
                        <div><?= esc($pesticide_data['block_site']) ?></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Farmer</label>
                        <div class="fw-bold">
                            <?= esc($pesticide_data['given_name'] . ' ' . $pesticide_data['surname']) ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Crop</label>
                        <div>
                            <span class="badge bg-success" style="background-color: <?= esc($pesticide_data['crop_color_code'] ?? '#28a745') ?> !important;">
                                <?= esc($pesticide_data['crop_name']) ?>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Location</label>
                        <div>
                            <small>
                                <i class="fas fa-map-pin me-1"></i><?= esc($pesticide_data['ward_name']) ?><br>
                                <i class="fas fa-location-dot me-1"></i><?= esc($pesticide_data['llg_name']) ?><br>
                                <i class="fas fa-building me-1"></i><?= esc($pesticide_data['district_name']) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Record Information -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Record Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label text-muted">Record ID</label>
                            <div class="fw-bold">#<?= $pesticide_data['id'] ?></div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label text-muted">Created Date</label>
                            <div><?= date('M d, Y H:i', strtotime($pesticide_data['created_at'])) ?></div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <div>
                                <?php if ($pesticide_data['updated_at'] && $pesticide_data['updated_at'] !== $pesticide_data['created_at']): ?>
                                    <?= date('M d, Y H:i', strtotime($pesticide_data['updated_at'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Never</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label text-muted">Created By</label>
                            <div>
                                <?php if (!empty($pesticide_data['created_by'])): ?>
                                    User #<?= $pesticide_data['created_by'] ?>
                                <?php else: ?>
                                    <span class="text-muted">System</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="post" style="display: none;">
    <?= csrf_field() ?>
</form>

<script>
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this pesticides data? This action cannot be undone.')) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('staff/farms/pesticides_data/') ?>' + id + '/delete';
        form.submit();
    }
}
</script>

<?= $this->endSection() ?> 