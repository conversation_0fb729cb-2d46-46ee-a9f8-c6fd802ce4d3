<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>">Livestock Blocks</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock/blocks/' . $block_id . '/data') ?>">Farm Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">View Details</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock/blocks/' . $block_id . '/data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/livestock/blocks/' . $block_id . '/data/' . $livestock_data['id'] . '/edit') ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Livestock Information Card -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Livestock Farm Data Details
                    </h5>
                    <p class="card-text mb-0 text-muted">Detailed information about this livestock record</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Livestock Type:</label>
                            <p class="mb-0">
                                <span class="badge bg-success fs-6"><?= esc($livestock_data['livestock_name']) ?></span>
                            </p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Breed:</label>
                            <p class="mb-0"><?= esc($livestock_data['breed']) ?></p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Action Date:</label>
                            <p class="mb-0">
                                <?php if (!empty($livestock_data['action_date'])): ?>
                                    <?= date('d F Y', strtotime($livestock_data['action_date'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Total Count:</label>
                            <p class="mb-0">
                                <span class="badge bg-info me-2">Male: <?= esc($livestock_data['he_total']) ?></span>
                                <span class="badge bg-warning">Female: <?= esc($livestock_data['she_total']) ?></span>
                                <br><small class="text-muted">Total: <?= ($livestock_data['he_total'] + $livestock_data['she_total']) ?></small>
                            </p>
                        </div>

                        <!-- Farm Management -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Pasture Type:</label>
                            <p class="mb-0"><?= esc($livestock_data['pasture_type']) ?: '<span class="text-muted">Not specified</span>' ?></p>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Growth Stage:</label>
                            <p class="mb-0"><?= esc($livestock_data['growth_stage']) ?: '<span class="text-muted">Not specified</span>' ?></p>
                        </div>

                        <!-- Financial Information -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Cost per Head:</label>
                            <p class="mb-0">
                                <?php if ($livestock_data['cost_per_livestock'] > 0): ?>
                                    <strong>K<?= number_format($livestock_data['cost_per_livestock'], 2) ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Lowest Price:</label>
                            <p class="mb-0">
                                <?php if ($livestock_data['low_price_per_livestock'] > 0): ?>
                                    <strong>K<?= number_format($livestock_data['low_price_per_livestock'], 2) ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Highest Price:</label>
                            <p class="mb-0">
                                <?php if ($livestock_data['high_price_per_livestock'] > 0): ?>
                                    <strong>K<?= number_format($livestock_data['high_price_per_livestock'], 2) ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <!-- Comments -->
                        <?php if (!empty($livestock_data['comments'])): ?>
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">Comments:</label>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($livestock_data['comments'])) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farm Block & Farmer Information -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Farm Block Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Block Code:</label>
                        <p class="mb-0"><strong><?= esc($livestock_data['block_code']) ?></strong></p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Block Site:</label>
                        <p class="mb-0"><?= esc($livestock_data['block_site']) ?></p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Farmer:</label>
                        <p class="mb-0">
                            <strong><?= esc($livestock_data['given_name'] . ' ' . $livestock_data['surname']) ?></strong><br>
                            <small class="text-muted">Code: <?= esc($livestock_data['farmer_code']) ?></small>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Location:</label>
                        <p class="mb-0">
                            <?= esc($livestock_data['village']) ?><br>
                            <small class="text-muted">
                                <?= esc($livestock_data['ward_name']) ?>, <?= esc($livestock_data['llg_name']) ?><br>
                                <?= esc($livestock_data['district_name']) ?>, <?= esc($livestock_data['province_name']) ?>
                            </small>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Record Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Record Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Status:</label>
                        <p class="mb-0">
                            <span class="badge bg-success">Active</span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Created:</label>
                        <p class="mb-0">
                            <small class="text-muted">
                                <?= date('d F Y, H:i', strtotime($livestock_data['created_at'])) ?>
                            </small>
                        </p>
                    </div>

                    <?php if (!empty($livestock_data['updated_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Last Updated:</label>
                            <p class="mb-0">
                                <small class="text-muted">
                                    <?= date('d F Y, H:i', strtotime($livestock_data['updated_at'])) ?>
                                </small>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
