<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\FertilizersModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\AdxDistrictModel;

class StaffFertilizerData extends BaseController
{
    protected $fertilizerDataModel;
    protected $fertilizersModel;
    protected $farmBlockModel;
    protected $farmerModel;
    protected $cropsModel;
    protected $districtModel;
    protected $helpers = ['url', 'form'];

    public function __construct()
    {
        // Check authentication and role
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        // Initialize models
        $this->fertilizerDataModel = new CropsFarmFertilizerDataModel();
        $this->fertilizersModel = new FertilizersModel();
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsModel = new CropsModel();
        $this->districtModel = new AdxDistrictModel();

        // Load helpers
        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    /**
     * Display list of farm blocks for fertilizer data management
     */
    public function index()
    {
        $districtId = session()->get('district_id');
        $district = $this->districtModel->find($districtId);
        $districtName = $district ? $district['name'] : 'Unknown District';

        // Get farm blocks with farmer and crop details
        $farmBlocks = $this->farmBlockModel->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', $districtId)
            ->where('crops_farm_blocks.status', 'active')
            ->orderBy('crops_farm_blocks.block_code', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Fertilizer Data Management',
            'page_header' => 'Fertilizer Data Management',
            'farm_blocks' => $farmBlocks,
            'district_name' => $districtName
        ];

        return view('staff_fertilizer_data/staff_fertilizer_data_index', $data);
    }

    /**
     * Show fertilizer data for a specific farm block
     */
    public function show($blockId)
    {
        $districtId = session()->get('district_id');
        
        // Get block details with farmer and crop information
        $block = $this->farmBlockModel->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.id', $blockId)
            ->where('crops_farm_blocks.district_id', $districtId)
            ->first();

        if (!$block) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Farm block not found or access denied.');
        }

        // Get fertilizer data for this block
        $fertilizerData = $this->fertilizerDataModel->select('
                crops_farm_fertilizer_data.*,
                adx_fertilizers.name as fertilizer_name,
                users.name as created_by_name
            ')
            ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
            ->join('users', 'users.id = crops_farm_fertilizer_data.created_by', 'left')
            ->where('crops_farm_fertilizer_data.block_id', $blockId)
            ->where('crops_farm_fertilizer_data.status', 'active')
            ->orderBy('crops_farm_fertilizer_data.action_date', 'DESC')
            ->findAll();

        // Get available fertilizers for dropdown
        $fertilizers = $this->fertilizersModel->findAll();

        $data = [
            'title' => 'Fertilizer Data - ' . $block['block_code'],
            'page_header' => 'Fertilizer Data for Block: ' . $block['block_code'],
            'block' => $block,
            'fertilizer_data' => $fertilizerData,
            'fertilizers' => $fertilizers
        ];

        return view('staff_fertilizer_data/staff_fertilizer_data_show', $data);
    }

    /**
     * Show form to create new fertilizer data
     */
    public function create($blockId)
    {
        $districtId = session()->get('district_id');
        
        // Get block details
        $block = $this->farmBlockModel->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_crops.crop_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->where('crops_farm_blocks.id', $blockId)
            ->where('crops_farm_blocks.district_id', $districtId)
            ->first();

        if (!$block) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Farm block not found or access denied.');
        }

        // Get available fertilizers
        $fertilizers = $this->fertilizersModel->findAll();

        $data = [
            'title' => 'Add Fertilizer Data - ' . $block['block_code'],
            'page_header' => 'Add Fertilizer Data for Block: ' . $block['block_code'],
            'block' => $block,
            'fertilizers' => $fertilizers
        ];

        return view('staff_fertilizer_data/staff_fertilizer_data_create', $data);
    }

    /**
     * Store new fertilizer data
     */
    public function store()
    {
        $blockId = $this->request->getPost('block_id');
        $districtId = session()->get('district_id');
        
        // Verify block belongs to user's district
        $block = $this->farmBlockModel->where('id', $blockId)
            ->where('district_id', $districtId)
            ->first();

        if (!$block) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Access denied.');
        }

        // Validation rules
        $rules = [
            'fertilizer_id' => 'required|numeric',
            'name' => 'required|max_length[255]',
            'brand' => 'required|max_length[255]',
            'unit_of_measure' => 'required|max_length[50]',
            'unit' => 'required|numeric|greater_than[0]',
            'quantity' => 'required|numeric|greater_than[0]',
            'action_date' => 'required|valid_date'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for insertion
        $data = [
            'exercise_id' => null,
            'block_id' => $blockId,
            'fertilizer_id' => $this->request->getPost('fertilizer_id'),
            'crop_id' => $block['crop_id'],
            'name' => $this->request->getPost('name'),
            'brand' => $this->request->getPost('brand'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('emp_id'),
            'status' => 'active'
        ];

        if ($this->fertilizerDataModel->save($data)) {
            return redirect()->to('staff/farms/fertilizer_data/show/' . $blockId)
                ->with('success', 'Fertilizer data added successfully.');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to save fertilizer data. Please try again.');
        }
    }

    /**
     * Show form to edit fertilizer data
     */
    public function edit($id)
    {
        $districtId = session()->get('district_id');
        
        // Get fertilizer data with block information
        $fertilizerData = $this->fertilizerDataModel->select('
                crops_farm_fertilizer_data.*,
                crops_farm_blocks.block_code,
                crops_farm_blocks.district_id,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_crops.crop_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->where('crops_farm_fertilizer_data.id', $id)
            ->where('crops_farm_blocks.district_id', $districtId)
            ->first();

        if (!$fertilizerData) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Fertilizer data not found or access denied.');
        }

        // Get available fertilizers
        $fertilizers = $this->fertilizersModel->findAll();

        $data = [
            'title' => 'Edit Fertilizer Data - ' . $fertilizerData['block_code'],
            'page_header' => 'Edit Fertilizer Data for Block: ' . $fertilizerData['block_code'],
            'fertilizer_data' => $fertilizerData,
            'fertilizers' => $fertilizers
        ];

        return view('staff_fertilizer_data/staff_fertilizer_data_edit', $data);
    }

    /**
     * Update fertilizer data
     */
    public function update($id)
    {
        $districtId = session()->get('district_id');
        
        // Verify access to this fertilizer data
        $existingData = $this->fertilizerDataModel->select('
                crops_farm_fertilizer_data.*,
                crops_farm_blocks.district_id,
                crops_farm_blocks.crop_id
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
            ->where('crops_farm_fertilizer_data.id', $id)
            ->where('crops_farm_blocks.district_id', $districtId)
            ->first();

        if (!$existingData) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Access denied.');
        }

        // Validation rules
        $rules = [
            'fertilizer_id' => 'required|numeric',
            'name' => 'required|max_length[255]',
            'brand' => 'required|max_length[255]',
            'unit_of_measure' => 'required|max_length[50]',
            'unit' => 'required|numeric|greater_than[0]',
            'quantity' => 'required|numeric|greater_than[0]',
            'action_date' => 'required|valid_date'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for update
        $data = [
            'fertilizer_id' => $this->request->getPost('fertilizer_id'),
            'name' => $this->request->getPost('name'),
            'brand' => $this->request->getPost('brand'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id')
        ];

        if ($this->fertilizerDataModel->update($id, $data)) {
            return redirect()->to('staff/farms/fertilizer_data/show/' . $existingData['block_id'])
                ->with('success', 'Fertilizer data updated successfully.');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to update fertilizer data. Please try again.');
        }
    }

    /**
     * Delete fertilizer data
     */
    public function destroy($id)
    {
        $districtId = session()->get('district_id');
        
        // Verify access to this fertilizer data
        $existingData = $this->fertilizerDataModel->select('
                crops_farm_fertilizer_data.*,
                crops_farm_blocks.district_id,
                crops_farm_blocks.block_code
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
            ->where('crops_farm_fertilizer_data.id', $id)
            ->where('crops_farm_blocks.district_id', $districtId)
            ->first();

        if (!$existingData) {
            return redirect()->to('staff/farms/fertilizer_data')->with('error', 'Access denied.');
        }

        // Soft delete the record
        $updateData = [
            'status' => 'deleted',
            'deleted_by' => session()->get('emp_id')
        ];

        if ($this->fertilizerDataModel->update($id, $updateData)) {
            return redirect()->to('staff/farms/fertilizer_data/show/' . $existingData['block_id'])
                ->with('success', 'Fertilizer data deleted successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to delete fertilizer data. Please try again.');
        }
    }
}
