<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Crop Details: Test Crop</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/reports/crops-data">Crops Data</a></li>
                    <li class="breadcrumb-item active">Test Crop</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>15</h3>
                        <p>Farm Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-th-large"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>45</h3>
                        <p>Farmers Involved</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>1,250</h3>
                        <p>Total Plants</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>85.5</h3>
                        <p>Total Hectares</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-map"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card card-success">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Farm Blocks Distribution
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="blocksChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Production Overview
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="productionChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card card-warning">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-2"></i>
                            Market Performance
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="marketChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card card-danger">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map mr-2"></i>
                            Geographic Distribution
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="geoChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
console.log('=== CHART DEBUG START ===');
console.log('1. Script tag loaded');

// Check if external resources are loaded
console.log('2. Checking external resources...');
console.log('   - jQuery loaded:', typeof $ !== 'undefined');
console.log('   - Bootstrap loaded:', typeof bootstrap !== 'undefined');
console.log('   - Chart.js loaded:', typeof Chart !== 'undefined');

// Check if DOM elements exist
console.log('3. Checking DOM elements...');
console.log('   - blocksChart exists:', !!document.getElementById('blocksChart'));
console.log('   - productionChart exists:', !!document.getElementById('productionChart'));
console.log('   - marketChart exists:', !!document.getElementById('marketChart'));
console.log('   - geoChart exists:', !!document.getElementById('geoChart'));

$(document).ready(function() {
    console.log('4. Document ready event fired');
    console.log('5. jQuery version:', $.fn.jquery);
    console.log('6. Calling initializeCharts...');
    initializeCharts();
});

function initializeCharts() {
    console.log('7. initializeCharts function called');

    // Debug: Check if Chart.js is loaded
    console.log('8. Checking Chart.js availability...');
    console.log('   - typeof Chart:', typeof Chart);
    console.log('   - Chart object:', Chart);
    console.log('   - Chart.js available:', typeof Chart !== 'undefined');

    if (typeof Chart !== 'undefined') {
        console.log('   - Chart version:', Chart.version);
        console.log('   - Chart defaults:', Chart.defaults);
    } else {
        console.error('   - ERROR: Chart.js is not loaded!');
        return;
    }

    // Test 1: Farm Blocks Distribution Chart (Doughnut)
    console.log('9. Creating Farm Blocks Distribution Chart...');
    const blocksData = [15, 45, 8]; // Active Blocks, Total Farmers, Districts Covered
    console.log('   - Blocks data:', blocksData);

    const blocksCanvas = document.getElementById('blocksChart');
    console.log('   - Blocks canvas element:', blocksCanvas);
    console.log('   - Canvas exists:', !!blocksCanvas);

    if (blocksCanvas) {
        console.log('   - Canvas found, getting context...');
        try {
            const blocksCtx = blocksCanvas.getContext('2d');
            console.log('   - Canvas context:', blocksCtx);
            console.log('   - Context type:', typeof blocksCtx);

            console.log('   - Creating Chart instance...');
            const chart = new Chart(blocksCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active Blocks', 'Total Farmers', 'Districts Covered'],
                    datasets: [{
                        data: blocksData,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            console.log('   - Blocks chart created successfully:', chart);
        } catch (error) {
            console.error('   - ERROR creating blocks chart:', error);
            console.error('   - Error stack:', error.stack);
        }
    } else {
        console.error('   - ERROR: Blocks canvas not found!');
    }

    // Test 2: Production Overview Chart (Bar)
    console.log('10. Creating Production Overview Chart...');
    const productionData = [1250, 85.5, 12, 8]; // Plants, Hectares, Harvests, Sales
    console.log('   - Production data:', productionData);

    const productionCanvas = document.getElementById('productionChart');
    console.log('   - Production canvas element:', productionCanvas);
    console.log('   - Canvas exists:', !!productionCanvas);

    if (productionCanvas) {
        console.log('   - Canvas found, getting context...');
        try {
            const productionCtx = productionCanvas.getContext('2d');
            console.log('   - Canvas context:', productionCtx);
            console.log('   - Creating Chart instance...');
            const productionChart = new Chart(productionCtx, {
                type: 'bar',
                data: {
                    labels: ['Plants', 'Hectares', 'Harvests', 'Sales'],
                    datasets: [{
                        label: 'Production Data',
                        data: productionData,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgb(75, 192, 192)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
            console.log('   - Production chart created successfully:', productionChart);
        } catch (error) {
            console.error('   - ERROR creating production chart:', error);
            console.error('   - Error stack:', error.stack);
        }
    } else {
        console.error('   - ERROR: Production canvas not found!');
    }

    // Test 3: Market Performance Chart (Line)
    console.log('11. Creating Market Performance Chart...');
    const marketData = [45000, 850, 8]; // Total Revenue (K), Total Quantity, Records
    console.log('   - Market data:', marketData);

    const marketCanvas = document.getElementById('marketChart');
    console.log('   - Market canvas element:', marketCanvas);
    console.log('   - Canvas exists:', !!marketCanvas);

    if (marketCanvas) {
        console.log('   - Canvas found, getting context...');
        try {
            const marketCtx = marketCanvas.getContext('2d');
            console.log('   - Canvas context:', marketCtx);
            console.log('   - Creating Chart instance...');
            const marketChart = new Chart(marketCtx, {
                type: 'line',
                data: {
                    labels: ['Total Revenue (K)', 'Total Quantity', 'Records'],
                    datasets: [{
                        label: 'Market Performance',
                        data: marketData,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
            console.log('   - Market chart created successfully:', marketChart);
        } catch (error) {
            console.error('   - ERROR creating market chart:', error);
            console.error('   - Error stack:', error.stack);
        }
    } else {
        console.error('   - ERROR: Market canvas not found!');
    }

    // Test 4: Geographic Distribution Chart (Pie)
    console.log('12. Creating Geographic Distribution Chart...');
    const geoLabels = ['Western Province', 'Southern Highlands', 'Eastern Highlands', 'Morobe', 'Central'];
    const geoData = [5, 3, 4, 2, 1];
    console.log('   - Geographic data:', geoData);

    const geoCanvas = document.getElementById('geoChart');
    console.log('   - Geographic canvas element:', geoCanvas);
    console.log('   - Canvas exists:', !!geoCanvas);

    if (geoCanvas) {
        console.log('   - Canvas found, getting context...');
        try {
            const geoCtx = geoCanvas.getContext('2d');
            console.log('   - Canvas context:', geoCtx);
            console.log('   - Creating Chart instance...');
            const geoChart = new Chart(geoCtx, {
                type: 'pie',
                data: {
                    labels: geoLabels,
                    datasets: [{
                        data: geoData,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            console.log('   - Geographic chart created successfully:', geoChart);
        } catch (error) {
            console.error('   - ERROR creating geographic chart:', error);
            console.error('   - Error stack:', error.stack);
        }
    } else {
        console.error('   - ERROR: Geographic canvas not found!');
    }

    console.log('=== CHART DEBUG END ===');
}
</script>
<?= $this->endSection() ?>
