<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">
                    <?php if (!empty($crop['crop_icon'])): ?>
                        <i class="<?= $crop['crop_icon'] ?> mr-2" 
                           style="color: <?= $crop['crop_color_code'] ?? '#28a745' ?>; font-size: 1.2em;"></i>
                    <?php endif; ?>
                    <?= $page_header ?>
                </h1>
                <p class="text-muted"><?= $page_desc ?></p>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/crops">Crops Data</a></li>
                    <li class="breadcrumb-item active"><?= esc($crop['crop_name']) ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        
        <!-- Crop Statistics Row -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-th-large"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Farm Blocks</span>
                        <span class="info-box-number"><?= number_format($crop_stats['blocks_count']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Farmers Involved</span>
                        <span class="info-box-number"><?= number_format($crop_stats['farmers_count']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-warning">
                    <span class="info-box-icon"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Plants</span>
                        <span class="info-box-number"><?= number_format($crop_stats['total_plants']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-danger">
                    <span class="info-box-icon"><i class="fas fa-chart-area"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Hectares</span>
                        <span class="info-box-number"><?= number_format($crop_stats['total_hectares'], 2) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Section -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card card-success">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Farm Blocks Distribution
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="blocksChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Production Overview
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="productionChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card card-warning">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-2"></i>
                            Market Performance
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="marketChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card card-danger">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-area mr-2"></i>
                            Geographic Distribution
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="chart">
                            <canvas id="geoChart" height="300" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Sections -->
        <!-- Farm Blocks Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-th-large mr-2"></i>
                            Farm Blocks
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="farm-blocks-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-success">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Location</th>
                                                <th>Block Site</th>
                                                <th>Village</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($farm_blocks as $block): ?>
                                            <tr>
                                                <td><strong><?= esc($block['block_code']) ?></strong></td>
                                                <td>
                                                    <?= esc($block['given_name'] . ' ' . $block['surname']) ?>
                                                    <br><small class="text-muted"><?= esc($block['farmer_code']) ?></small>
                                                </td>
                                                <td>
                                                    <?= esc($block['district_name']) ?><br>
                                                    <small class="text-muted"><?= esc($block['llg_name']) ?> - <?= esc($block['ward_name']) ?></small>
                                                </td>
                                                <td><?= esc($block['block_site']) ?></td>
                                                <td><?= esc($block['village']) ?></td>
                                                <td>
                                                    <?php if ($block['status'] == 'active'): ?>
                                                        <span class="badge badge-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-secondary"><?= ucfirst($block['status']) ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= date('M d, Y', strtotime($block['created_at'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users mr-2"></i>
                            Farmers Involved
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="farmers-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-info">
                                            <tr>
                                                <th>Farmer Code</th>
                                                <th>Name</th>
                                                <th>Gender</th>
                                                <th>Location</th>
                                                <th>Blocks Count</th>
                                                <th>Contact</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($farmers_involved as $farmer): ?>
                                            <tr>
                                                <td><strong><?= esc($farmer['farmer_code']) ?></strong></td>
                                                <td><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></td>
                                                <td>
                                                    <span class="badge badge-<?= $farmer['gender'] == 'Male' ? 'primary' : 'pink' ?>">
                                                        <?= esc($farmer['gender']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?= esc($farmer['district_name']) ?><br>
                                                    <small class="text-muted"><?= esc($farmer['llg_name']) ?> - <?= esc($farmer['ward_name']) ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge badge-warning badge-lg">
                                                        <?= number_format($farmer['blocks_count']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($farmer['phone'])): ?>
                                                        <i class="fas fa-phone mr-1"></i><?= esc($farmer['phone']) ?><br>
                                                    <?php endif; ?>
                                                    <?php if (!empty($farmer['email'])): ?>
                                                        <i class="fas fa-envelope mr-1"></i><?= esc($farmer['email']) ?>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Diseases Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-danger card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bug mr-2"></i>
                            Diseases Data
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="diseases-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-danger">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Disease Name</th>
                                                <th>Description</th>
                                                <th>Plants Affected</th>
                                                <th>Hectares</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($diseases_data as $disease): ?>
                                            <tr>
                                                <td><?= esc($disease['block_code']) ?></td>
                                                <td><?= esc($disease['given_name'] . ' ' . $disease['surname']) ?></td>
                                                <td><strong><?= esc($disease['disease_name']) ?></strong></td>
                                                <td><?= esc($disease['description']) ?></td>
                                                <td><?= number_format($disease['number_of_plants']) ?></td>
                                                <td><?= number_format($disease['hectares'], 2) ?> ha</td>
                                                <td><?= date('M d, Y', strtotime($disease['action_date'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fertilizers Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-warning card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-flask mr-2"></i>
                            Fertilizers Data
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="fertilizers-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-warning">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Fertilizer</th>
                                                <th>Brand</th>
                                                <th>Quantity</th>
                                                <th>Unit</th>
                                                <th>Date Applied</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($fertilizers_data as $fertilizer): ?>
                                            <tr>
                                                <td><?= esc($fertilizer['block_code']) ?></td>
                                                <td><?= esc($fertilizer['given_name'] . ' ' . $fertilizer['surname']) ?></td>
                                                <td><strong><?= esc($fertilizer['name']) ?></strong></td>
                                                <td><?= esc($fertilizer['brand']) ?></td>
                                                <td><?= number_format($fertilizer['quantity'], 2) ?></td>
                                                <td><?= esc($fertilizer['unit']) ?></td>
                                                <td><?= date('M d, Y', strtotime($fertilizer['action_date'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pesticides Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-secondary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-spray-can mr-2"></i>
                            Pesticides Data
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="pesticides-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-secondary">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Pesticide</th>
                                                <th>Brand</th>
                                                <th>Quantity</th>
                                                <th>Unit</th>
                                                <th>Date Applied</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($pesticides_data as $pesticide): ?>
                                            <tr>
                                                <td><?= esc($pesticide['block_code']) ?></td>
                                                <td><?= esc($pesticide['given_name'] . ' ' . $pesticide['surname']) ?></td>
                                                <td><strong><?= esc($pesticide['name']) ?></strong></td>
                                                <td><?= esc($pesticide['brand']) ?></td>
                                                <td><?= number_format($pesticide['quantity'], 2) ?></td>
                                                <td><?= esc($pesticide['unit']) ?></td>
                                                <td><?= date('M d, Y', strtotime($pesticide['action_date'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Harvest Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-warehouse mr-2"></i>
                            Harvest Data
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="harvest-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-success">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Item</th>
                                                <th>Description</th>
                                                <th>Quantity</th>
                                                <th>Unit</th>
                                                <th>Harvest Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($harvest_data as $harvest): ?>
                                            <tr>
                                                <td><?= esc($harvest['block_code']) ?></td>
                                                <td><?= esc($harvest['given_name'] . ' ' . $harvest['surname']) ?></td>
                                                <td><strong><?= esc($harvest['item']) ?></strong></td>
                                                <td><?= esc($harvest['description']) ?></td>
                                                <td><?= number_format($harvest['quantity'], 2) ?></td>
                                                <td><?= esc($harvest['unit']) ?></td>
                                                <td><?= date('M d, Y', strtotime($harvest['harvest_date'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Marketing Section -->
        <div class="row">
            <div class="col-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line mr-2"></i>
                            Marketing Data
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                                <div class="table-responsive">
                                    <table id="marketing-table" class="table table-bordered table-striped table-hover">
                                        <thead class="bg-primary">
                                            <tr>
                                                <th>Block Code</th>
                                                <th>Farmer</th>
                                                <th>Product</th>
                                                <th>Buyer</th>
                                                <th>Quantity</th>
                                                <th>Price/Unit</th>
                                                <th>Total Value</th>
                                                <th>Market Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($marketing_data as $market): ?>
                                            <tr>
                                                <td><?= esc($market['block_code']) ?></td>
                                                <td><?= esc($market['given_name'] . ' ' . $market['surname']) ?></td>
                                                <td><strong><?= esc($market['product']) ?></strong></td>
                                                <td><?= esc($market['buyer_name'] ?? 'N/A') ?></td>
                                                <td><?= number_format($market['quantity'], 2) ?> <?= esc($market['unit']) ?></td>
                                                <td>K <?= number_format($market['market_price_per_unit'], 2) ?></td>
                                                <td>K <?= number_format($market['quantity'] * $market['market_price_per_unit'], 2) ?></td>
                                                <td><?= date('M d, Y', strtotime($market['market_date'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {

    // Initialize all DataTables
    var tableConfig = {
        "responsive": true,
        "scrollX": true,
        "scrollCollapse": true,
        "autoWidth": false,
        "pageLength": 15,
        "fixedHeader": true,
        "dom": "<'row'<'col-sm-12 col-md-6'B><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                extend: 'excel',
                className: 'btn-sm btn-success',
                text: '<i class="fas fa-file-excel mr-1"></i> Excel'
            },
            {
                extend: 'pdf',
                className: 'btn-sm btn-danger',
                text: '<i class="fas fa-file-pdf mr-1"></i> PDF',
                orientation: 'landscape'
            },
            {
                extend: 'print',
                className: 'btn-sm btn-info',
                text: '<i class="fas fa-print mr-1"></i> Print'
            }
        ]
    };

    // Initialize each table
    $('#farm-blocks-table').DataTable(tableConfig);
    $('#farmers-table').DataTable(tableConfig);
    $('#diseases-table').DataTable(tableConfig);
    $('#fertilizers-table').DataTable(tableConfig);
    $('#pesticides-table').DataTable(tableConfig);
    $('#harvest-table').DataTable(tableConfig);
    $('#marketing-table').DataTable(tableConfig);

    // Add buttons to each table
    $('[id$="-table"]').each(function() {
        $(this).DataTable().buttons().container().appendTo('#' + this.id + '_wrapper .col-md-6:eq(0)');
    });

    // Initialize Charts
    initializeCharts();
});

function initializeCharts() {
    // Debug: Check if Chart.js is loaded
    console.log('Chart.js available:', typeof Chart !== 'undefined');
    console.log('Chart version:', Chart ? Chart.version : 'Not available');

    // Farm Blocks Distribution Chart
    const blocksData = [<?= $crop_stats['blocks_count'] ?>, <?= $crop_stats['farmers_count'] ?>, <?= count(array_unique(array_column($farm_blocks, 'district_name'))) ?>];
    console.log('Blocks data:', blocksData);

    const blocksCanvas = document.getElementById('blocksChart');
    console.log('Blocks canvas element:', blocksCanvas);

    if (blocksData.every(val => val === 0)) {
        document.getElementById('blocksChart').parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No data available</div>';
    } else {
        try {
            const blocksCtx = blocksCanvas.getContext('2d');
            console.log('Canvas context:', blocksCtx);

            const chart = new Chart(blocksCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active Blocks', 'Total Farmers', 'Districts Covered'],
                    datasets: [{
                        data: blocksData,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            console.log('Chart created successfully:', chart);
        } catch (error) {
            console.error('Error creating blocks chart:', error);
            document.getElementById('blocksChart').parentElement.innerHTML = '<div class="text-center text-danger p-4"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><br>Error loading chart: ' + error.message + '</div>';
        }
    }

    // Production Overview Chart
    const productionData = [
        <?= $crop_stats['total_plants'] ?>,
        <?= $crop_stats['total_hectares'] ?>,
        <?= count($harvest_data) ?>,
        <?= count($marketing_data) ?>
    ];
    console.log('Production data:', productionData);

    if (productionData.every(val => val === 0)) {
        document.getElementById('productionChart').parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-bar fa-3x mb-3"></i><br>No production data available</div>';
    } else {
        try {
            const productionCtx = document.getElementById('productionChart').getContext('2d');
            new Chart(productionCtx, {
                type: 'bar',
                data: {
                    labels: ['Plants', 'Hectares', 'Harvests', 'Sales'],
                    datasets: [{
                        label: 'Production Data',
                        data: productionData,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgb(75, 192, 192)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            console.log('Production chart created successfully');
        } catch (error) {
            console.error('Error creating production chart:', error);
            document.getElementById('productionChart').parentElement.innerHTML = '<div class="text-center text-danger p-4"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><br>Error loading chart: ' + error.message + '</div>';
        }
    }

    // Market Performance Chart
    <?php
    $totalRevenue = 0;
    $totalQuantity = 0;
    foreach($marketing_data as $market) {
        $totalRevenue += ($market['quantity'] * $market['market_price_per_unit']);
        $totalQuantity += $market['quantity'];
    }
    ?>
    const marketData = [<?= $totalRevenue ?>, <?= $totalQuantity ?>, <?= count($marketing_data) ?>];

    if (marketData.every(val => val === 0)) {
        document.getElementById('marketChart').parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-line fa-3x mb-3"></i><br>No market data available</div>';
    } else {
        const marketCtx = document.getElementById('marketChart').getContext('2d');
        new Chart(marketCtx, {
            type: 'line',
            data: {
                labels: ['Total Revenue (K)', 'Total Quantity', 'Records'],
                datasets: [{
                    label: 'Market Performance',
                    data: marketData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Geographic Distribution Chart
    const geoLabels = [
        <?php
        $districts = array_unique(array_column($farm_blocks, 'district_name'));
        foreach($districts as $index => $district): ?>
            '<?= esc($district) ?>'<?= $index < count($districts) - 1 ? ',' : '' ?>
        <?php endforeach; ?>
    ];
    const geoData = [
        <?php
        $districtCounts = array_count_values(array_column($farm_blocks, 'district_name'));
        $counts = array_values($districtCounts);
        foreach($counts as $index => $count): ?>
            <?= $count ?><?= $index < count($counts) - 1 ? ',' : '' ?>
        <?php endforeach; ?>
    ];

    if (geoData.length === 0 || geoData.every(val => val === 0)) {
        document.getElementById('geoChart').parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-map fa-3x mb-3"></i><br>No geographic data available</div>';
    } else {
        const geoCtx = document.getElementById('geoChart').getContext('2d');
        new Chart(geoCtx, {
            type: 'pie',
            data: {
                labels: geoLabels,
                datasets: [{
                    data: geoData,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
}
</script>
<?= $this->endSection() ?>
