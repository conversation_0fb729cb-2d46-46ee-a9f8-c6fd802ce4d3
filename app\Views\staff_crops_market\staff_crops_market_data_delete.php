<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Delete Crops Market Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/marketing_data') ?>">Crops Market Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Delete Item</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Confirm Deletion</h5>
                </div>
                <div class="card-body">
                    <p class="lead">Are you sure you want to delete the following market data entry?</p>
                    <dl class="row">
                        <dt class="col-sm-3">ID</dt>
                        <dd class="col-sm-9"><?= esc($marketingData['id']) ?></dd>
                        <dt class="col-sm-3">Crop</dt>
                        <dd class="col-sm-9"><?= esc($marketingData['crop_name']) ?></dd>
                        <dt class="col-sm-3">Market Date</dt>
                        <dd class="col-sm-9"><?= esc($marketingData['market_date']) ?></dd>
                        <dt class="col-sm-3">Selling Location</dt>
                        <dd class="col-sm-9"><?= esc($marketingData['selling_location']) ?></dd>
                        <dt class="col-sm-3">Status</dt>
                        <dd class="col-sm-9">
                            <span class="badge <?= $marketingData['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                <?= esc(ucfirst($marketingData['status'])) ?>
                            </span>
                        </dd>
                    </dl>
                    <form action="<?= base_url('staff/farms/marketing_data/' . esc($marketingData['id'])) ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="_method" value="delete">
                        <button type="submit" class="btn btn-danger me-2">
                            <i class="fas fa-trash me-2"></i>Delete
                        </button>
                        <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
