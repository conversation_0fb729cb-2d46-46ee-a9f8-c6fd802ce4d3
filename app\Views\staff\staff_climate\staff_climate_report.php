<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800"><?= $page_header ?></h1>
                    <p class="mb-0 text-muted">View historical weather data for any location (optimized for faster loading)</p>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/tools/climate-data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Climate Focus Location
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Data Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cloud-sun me-2"></i>Climate Data Parameters
                    </h6>
                </div>
                <div class="card-body">
                    <form id="weatherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="gps" class="form-label">GPS Coordinates (latitude,longitude) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="gps" name="gps"
                                       placeholder="e.g., -6.314993, 143.95555" required>
                                <div class="form-text">Enter GPS coordinates for the location you want to analyze</div>
                            </div>
                            <div class="col-md-6">
                                <label for="monthsAgo" class="form-label">Number of Months <span class="text-danger">*</span></label>
                                <select class="form-control" id="monthsAgo" name="monthsAgo" required>
                                    <option value="1">1 Month</option>
                                    <option value="3" selected>3 Months (Recommended)</option>
                                    <option value="6">6 Months</option>
                                </select>
                                <div class="form-text">Select data period (shorter periods load faster)</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="submitButton">
                                    <span class="button-text">
                                        <i class="fas fa-search me-2"></i>Get Climate Data
                                    </span>
                                    <div class="spinner-border spinner-border-sm d-none" role="status" id="loadingSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-danger d-none mt-3" id="errorMessage" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorText"></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="resultsSection" class="d-none">
        <!-- Location Map Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>Location Map
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="map" style="height: 300px; width: 100%;"></div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="locationInfo">Map will show the location of entered GPS coordinates</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Climate Summary Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-line me-2"></i>Climate Data Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="overallSummary">
                            <!-- Overall summary will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Temperature Trends</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="temperatureChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Humidity Levels</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="humidityChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Precipitation</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="precipitationChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Wind Speed</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="windspeedChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 3 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Cloud Cover</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="cloudChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Clear Sky Sunshine Hours</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="sunshineChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weekly Averages Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Weekly Weather Averages</h6>
                        <div>
                            <button class="btn btn-info btn-sm me-2" id="downloadButton">
                                <i class="fas fa-download me-2"></i>Download Daily Data
                            </button>
                            <button class="btn btn-success btn-sm" id="copyButton">
                                <i class="fas fa-copy me-2"></i>Copy Table
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-sm" id="weatherTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Week Period</th>
                                        <th>Avg Max Temp (°C)</th>
                                        <th>Avg Min Temp (°C)</th>
                                        <th>Avg Humidity (%)</th>
                                        <th>Total Precipitation (mm)</th>
                                        <th>Avg Wind Speed (km/h)</th>
                                        <th>Avg Cloud Cover (%)</th>
                                        <th>Avg Clear Sky (hrs/day)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rows will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS for OpenStreetMap -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Initialize chart instances and map
let temperatureChart, humidityChart, precipitationChart, windspeedChart, cloudChart, sunshineChart;
let map, marker;
let fullWeatherData = []; // Store full data for download

document.getElementById('weatherForm').addEventListener('submit', function(event) {
    event.preventDefault();

    // Set loading state
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');

    submitButton.disabled = true;
    buttonText.classList.add('d-none');
    loadingSpinner.classList.remove('d-none');
    errorMessage.classList.add('d-none');

    const gps = document.getElementById('gps').value;
    const monthsAgo = parseInt(document.getElementById('monthsAgo').value);

    const [latitude, longitude] = gps.split(',').map(coord => parseFloat(coord.trim()));

    if (isNaN(latitude) || isNaN(longitude) || isNaN(monthsAgo)) {
        showError('Please enter valid GPS coordinates and select a time period.');
        resetButton();
        return;
    }

    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - monthsAgo);

    const formatDate = (date) => date.toISOString().split('T')[0];

    // Use daily data for better performance with all weather parameters
    const url = `https://archive-api.open-meteo.com/v1/archive?latitude=${latitude}&longitude=${longitude}&start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&daily=temperature_2m_max,temperature_2m_min,relative_humidity_2m_mean,precipitation_sum,windspeed_10m_max,cloudcover_mean&timezone=auto`;

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                showError(`Error: ${data.reason}`);
            } else {
                const daily = data.daily;

                // Store full data for download
                fullWeatherData = daily;

                // Process data
                const dates = daily.time.map(date => new Date(date).toLocaleDateString());
                const maxTemps = daily.temperature_2m_max;
                const minTemps = daily.temperature_2m_min;
                const humidity = daily.relative_humidity_2m_mean;
                const precipitation = daily.precipitation_sum;
                const windSpeed = daily.windspeed_10m_max;
                const cloudCover = daily.cloudcover_mean;
                // Calculate clear sky sunshine hours based on cloud cover (more accurate)
                const sunshineHours = cloudCover.map(cloudPercent => {
                    // Assume 12 hours of potential daylight
                    const maxDaylight = 12;
                    // Clear sky percentage = 100 - cloud cover percentage
                    const clearSkyPercent = Math.max(0, 100 - (cloudPercent || 0));
                    // Calculate sunshine hours: clear sky percentage of max daylight
                    return parseFloat(((clearSkyPercent / 100) * maxDaylight).toFixed(1));
                });

                // Destroy existing charts if they exist
                if (temperatureChart) temperatureChart.destroy();
                if (humidityChart) humidityChart.destroy();
                if (precipitationChart) precipitationChart.destroy();
                if (windspeedChart) windspeedChart.destroy();
                if (cloudChart) cloudChart.destroy();
                if (sunshineChart) sunshineChart.destroy();

                // Show results section
                document.getElementById('resultsSection').classList.remove('d-none');

                // Initialize and update map
                initializeMap(latitude, longitude);

                // Create charts
                createCharts(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours);

                // Generate and display summary
                generateSummary(maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours);

                // Populate table with weekly averages
                populateTable(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours);
            }
        })
        .catch(error => {
            console.error('Error fetching weather data:', error);
            showError('Failed to fetch weather data. Please check your internet connection and try again.');
        })
        .finally(() => {
            resetButton();
        });
});

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorMessage.classList.remove('d-none');
}

function resetButton() {
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');

    submitButton.disabled = false;
    buttonText.classList.remove('d-none');
    loadingSpinner.classList.add('d-none');
}

function initializeMap(latitude, longitude) {
    // Initialize map if it doesn't exist
    if (!map) {
        map = L.map('map').setView([latitude, longitude], 10);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);
    } else {
        // Update existing map view
        map.setView([latitude, longitude], 10);
    }

    // Remove existing marker if any
    if (marker) {
        map.removeLayer(marker);
    }

    // Add new marker
    marker = L.marker([latitude, longitude]).addTo(map)
        .bindPopup(`<b>Climate Data Location</b><br>Coordinates: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`)
        .openPopup();

    // Update location info
    document.getElementById('locationInfo').innerHTML =
        `<i class="fas fa-map-marker-alt me-1"></i>Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;

    // Ensure map renders properly
    setTimeout(() => {
        map.invalidateSize();
    }, 100);
}

function generateSummary(maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours) {
    // Calculate overall statistics
    const maxTempStats = calculateStats(maxTemps);
    const minTempStats = calculateStats(minTemps);
    const humidityStats = calculateStats(humidity);
    const precipStats = calculateStats(precipitation);
    const windStats = calculateStats(windSpeed);
    const cloudStats = calculateStats(cloudCover);

    // Calculate sunshine statistics (daily hours of clear sky)
    const sunshineStats = calculateStats(sunshineHours);

    // Display overall summary
    const overallSummary = document.getElementById('overallSummary');
    overallSummary.innerHTML = `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-danger h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Temperature Range</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Max: ${maxTempStats.avg.toFixed(1)}°C | Min: ${minTempStats.avg.toFixed(1)}°C</div>
                    <div class="text-xs text-gray-600">Peak: ${maxTempStats.max.toFixed(1)}°C | Lowest: ${minTempStats.min.toFixed(1)}°C</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-info h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Humidity</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${humidityStats.avg.toFixed(1)}%</div>
                    <div class="text-xs text-gray-600">High: ${humidityStats.max.toFixed(1)}% | Low: ${humidityStats.min.toFixed(1)}%</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Precipitation</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Total: ${precipStats.sum.toFixed(1)}mm</div>
                    <div class="text-xs text-gray-600">Max: ${precipStats.max.toFixed(1)}mm | Avg: ${precipStats.avg.toFixed(1)}mm</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-warning h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Wind Speed</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${windStats.avg.toFixed(1)} km/h</div>
                    <div class="text-xs text-gray-600">Max: ${windStats.max.toFixed(1)} km/h | Min: ${windStats.min.toFixed(1)} km/h</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-primary h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Cloud Cover</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${cloudStats.avg.toFixed(1)}%</div>
                    <div class="text-xs text-gray-600">Max: ${cloudStats.max.toFixed(1)}% | Min: ${cloudStats.min.toFixed(1)}%</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-warning h-100" style="border-left-color: #f39c12 !important;">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-uppercase mb-1" style="color: #f39c12;">Sunshine Duration</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${sunshineStats.avg.toFixed(1)} hrs/day</div>
                    <div class="text-xs text-gray-600">Max: ${sunshineStats.max.toFixed(1)} hrs | Min: ${sunshineStats.min.toFixed(1)} hrs</div>
                </div>
            </div>
        </div>
    `;
}

function calculateStats(data) {
    // Convert all values to numbers and filter out invalid ones
    const validData = data
        .map(val => {
            if (typeof val === 'string') {
                return parseFloat(val);
            }
            return val;
        })
        .filter(val => val !== null && val !== undefined && !isNaN(val) && isFinite(val));

    if (validData.length === 0) return { avg: 0, min: 0, max: 0, sum: 0 };

    const sum = validData.reduce((a, b) => a + b, 0);
    const avg = sum / validData.length;
    const min = Math.min(...validData);
    const max = Math.max(...validData);

    return { avg, min, max, sum };
}



function createCharts(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours) {
    // Temperature Chart with both max and min temperatures
    temperatureChart = new Chart(document.getElementById('temperatureChart'), {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Max Temperature (°C)',
                data: maxTemps,
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                fill: false,
                tension: 0.1
            }, {
                label: 'Min Temperature (°C)',
                data: minTemps,
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Temperature (°C)' } }
            }
        }
    });

    // Humidity Chart
    humidityChart = new Chart(document.getElementById('humidityChart'), {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Humidity (%)',
                data: humidity,
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Humidity (%)' } }
            }
        }
    });

    // Precipitation Chart
    precipitationChart = new Chart(document.getElementById('precipitationChart'), {
        type: 'bar',
        data: {
            labels: dates,
            datasets: [{
                label: 'Precipitation (mm)',
                data: precipitation,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.6)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Precipitation (mm)' }, beginAtZero: true }
            }
        }
    });

    // Wind Speed Chart
    windspeedChart = new Chart(document.getElementById('windspeedChart'), {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Wind Speed (km/h)',
                data: windSpeed,
                borderColor: 'rgba(153, 102, 255, 1)',
                backgroundColor: 'rgba(153, 102, 255, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Wind Speed (km/h)' } }
            }
        }
    });

    // Cloud Cover Chart
    cloudChart = new Chart(document.getElementById('cloudChart'), {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Cloud Cover (%)',
                data: cloudCover,
                borderColor: 'rgba(128, 128, 128, 1)',
                backgroundColor: 'rgba(128, 128, 128, 0.2)',
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Cloud Cover (%)' }, min: 0, max: 100 }
            }
        }
    });

    // Clear Sky Sunshine Hours Chart
    sunshineChart = new Chart(document.getElementById('sunshineChart'), {
        type: 'bar',
        data: {
            labels: dates,
            datasets: [{
                label: 'Clear Sky Hours',
                data: sunshineHours,
                borderColor: 'rgba(255, 193, 7, 1)',
                backgroundColor: 'rgba(255, 193, 7, 0.6)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date' } },
                y: { display: true, title: { display: true, text: 'Clear Sky Hours' }, beginAtZero: true, max: 14 }
            }
        }
    });
}

function populateTable(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours) {
    const tableBody = document.querySelector('#weatherTable tbody');
    tableBody.innerHTML = ''; // Clear previous data

    // Group data by weeks
    const weeklyData = groupDataByWeeks(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours);

    weeklyData.forEach(week => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${week.period}</td>
            <td>${week.avgMaxTemp.toFixed(1)}</td>
            <td>${week.avgMinTemp.toFixed(1)}</td>
            <td>${week.avgHumidity.toFixed(1)}</td>
            <td>${week.totalPrecipitation.toFixed(1)}</td>
            <td>${week.avgWindSpeed.toFixed(1)}</td>
            <td>${week.avgCloudCover.toFixed(1)}</td>
            <td>${week.avgSunshine.toFixed(1)}</td>
        `;
        tableBody.appendChild(row);
    });
}

function groupDataByWeeks(dates, maxTemps, minTemps, humidity, precipitation, windSpeed, cloudCover, sunshineHours) {
    const weeks = [];
    let currentWeek = [];
    let weekStartIndex = 0;

    for (let i = 0; i < dates.length; i++) {
        currentWeek.push({
            dateStr: dates[i],
            maxTemp: maxTemps[i] || 0,
            minTemp: minTemps[i] || 0,
            humidity: humidity[i] || 0,
            precipitation: precipitation[i] || 0,
            windSpeed: windSpeed[i] || 0,
            cloudCover: cloudCover[i] || 0,
            sunshine: sunshineHours[i] || 0
        });

        // Group every 7 days or when we reach the end
        if (currentWeek.length === 7 || i === dates.length - 1) {
            weeks.push(processWeekData(currentWeek));
            currentWeek = [];
            weekStartIndex = i + 1;
        }
    }

    return weeks;
}

function processWeekData(weekData) {
    if (weekData.length === 0) return null;

    const firstDate = weekData[0].dateStr;
    const lastDate = weekData[weekData.length - 1].dateStr;

    // Calculate averages and totals
    const avgMaxTemp = weekData.reduce((sum, day) => sum + day.maxTemp, 0) / weekData.length;
    const avgMinTemp = weekData.reduce((sum, day) => sum + day.minTemp, 0) / weekData.length;
    const avgHumidity = weekData.reduce((sum, day) => sum + day.humidity, 0) / weekData.length;
    const totalPrecipitation = weekData.reduce((sum, day) => sum + day.precipitation, 0);
    const avgWindSpeed = weekData.reduce((sum, day) => sum + day.windSpeed, 0) / weekData.length;
    const avgCloudCover = weekData.reduce((sum, day) => sum + day.cloudCover, 0) / weekData.length;
    const avgSunshine = weekData.reduce((sum, day) => sum + day.sunshine, 0) / weekData.length;

    return {
        period: `${firstDate} - ${lastDate}`,
        avgMaxTemp,
        avgMinTemp,
        avgHumidity,
        totalPrecipitation,
        avgWindSpeed,
        avgCloudCover,
        avgSunshine
    };
}

// Copy Table Data to Clipboard
document.getElementById('copyButton').addEventListener('click', function() {
    const table = document.getElementById('weatherTable');
    let text = '';

    // Extract table headers
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.innerText);
    text += headers.join('\t') + '\n';

    // Extract table rows
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = Array.from(row.querySelectorAll('td')).map(td => td.innerText);
        text += rowData.join('\t') + '\n';
    });

    // Copy to clipboard
    navigator.clipboard.writeText(text)
        .then(() => {
            // Show success message
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            this.classList.remove('btn-success');
            this.classList.add('btn-success');

            setTimeout(() => {
                this.innerHTML = originalText;
            }, 2000);
        })
        .catch(() => {
            alert('Failed to copy table data.');
        });
});

// Download Daily Data as CSV
document.getElementById('downloadButton').addEventListener('click', function() {
    if (!fullWeatherData || !fullWeatherData.time) {
        alert('No data available for download.');
        return;
    }

    let csvContent = 'Date,Max Temperature (°C),Min Temperature (°C),Humidity (%),Precipitation (mm),Wind Speed (km/h),Cloud Cover (%),Clear Sky Hours\n';

    for (let i = 0; i < fullWeatherData.time.length; i++) {
        const date = new Date(fullWeatherData.time[i]).toLocaleDateString();
        const maxTemp = fullWeatherData.temperature_2m_max[i] || 0;
        const minTemp = fullWeatherData.temperature_2m_min[i] || 0;
        const humidity = fullWeatherData.relative_humidity_2m_mean[i] || 0;
        const precip = fullWeatherData.precipitation_sum[i] || 0;
        const wind = fullWeatherData.windspeed_10m_max[i] || 0;
        const cloud = fullWeatherData.cloudcover_mean[i] || 0;
        const cloudPercent = fullWeatherData.cloudcover_mean[i] || 0;
        const clearSkyPercent = Math.max(0, 100 - cloudPercent);
        const sunshine = ((clearSkyPercent / 100) * 12).toFixed(1); // 12 hours max daylight

        csvContent += `${date},${maxTemp},${minTemp},${humidity},${precip},${wind},${cloud},${sunshine}\n`;
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `climate_data_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // Show success message
    const originalText = this.innerHTML;
    this.innerHTML = '<i class="fas fa-check me-2"></i>Downloaded!';
    setTimeout(() => {
        this.innerHTML = originalText;
    }, 2000);
});
</script>
<?= $this->endSection() ?>
