<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/field-visits') ?>" class="text-success">Field Visits</a></li>
                            <li class="breadcrumb-item active" aria-current="page">View</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/extension/field-visits/' . $visit['id'] . '/edit') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                    <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Field Visit Details
                    </h5>
                    <p class="card-text mb-0 text-muted">Field Visit ID: <?= $visit['id'] ?></p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Basic Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 40%">ID:</th>
                                    <td><?= $visit['id'] ?></td>
                                </tr>
                                <tr>
                                    <th>LLG:</th>
                                    <td><?= esc($visit['llg_name']) ?></td>
                                </tr>
                                <tr>
                                    <th>District:</th>
                                    <td><?= esc($visit['district_name']) ?></td>
                                </tr>
                                <tr>
                                    <th>Province:</th>
                                    <td><?= esc($visit['province_name']) ?></td>
                                </tr>
                                <tr>
                                    <th>Purpose:</th>
                                    <td><?= esc($visit['purpose']) ?></td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <?php if ($visit['status'] == 1): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Date Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Date Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 40%">Start Date:</th>
                                    <td><?= date('d M Y', strtotime($visit['date_start'])) ?></td>
                                </tr>
                                <tr>
                                    <th>End Date:</th>
                                    <td><?= date('d M Y', strtotime($visit['date_end'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Duration:</th>
                                    <td>
                                        <?php
                                        $start = new DateTime($visit['date_start']);
                                        $end = new DateTime($visit['date_end']);
                                        $diff = $start->diff($end);
                                        echo $diff->days + 1 . ' day(s)';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td><?= date('d M Y H:i', strtotime($visit['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td><?= date('d M Y H:i', strtotime($visit['updated_at'])) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <!-- Location Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Location Information</h6>
                            
                            <div class="mb-3">
                                <strong>GPS Coordinates:</strong><br>
                                <?php if (!empty($visit['gps'])): ?>
                                    <span class="text-monospace"><?= esc($visit['gps']) ?></span>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <strong>Specific Locations:</strong><br>
                                <?php
                                $locations = [];
                                if (!empty($visit['locations'])) {
                                    if (is_string($visit['locations'])) {
                                        $locationsData = json_decode($visit['locations'], true);
                                    } else {
                                        $locationsData = $visit['locations'];
                                    }
                                    
                                    if (is_array($locationsData)) {
                                        $locations = $locationsData;
                                    }
                                }
                                ?>
                                <?php if (!empty($locations)): ?>
                                    <ul class="list-unstyled">
                                        <?php foreach ($locations as $location): ?>
                                            <li><i class="fas fa-map-marker-alt text-success me-2"></i><?= esc($location) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <span class="text-muted">No specific locations recorded</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Officers Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Participating Officers</h6>
                            
                            <?php
                            $officers = [];
                            if (!empty($visit['officers'])) {
                                if (is_string($visit['officers'])) {
                                    $officersData = json_decode($visit['officers'], true);
                                } else {
                                    $officersData = $visit['officers'];
                                }
                                
                                if (is_array($officersData)) {
                                    $officers = $officersData;
                                }
                            }
                            ?>
                            
                            <?php if (!empty($officers)): ?>
                                <ul class="list-unstyled">
                                    <?php foreach ($officers as $officer): ?>
                                        <li class="mb-2">
                                            <i class="fas fa-user text-primary me-2"></i>
                                            <strong><?= esc($officer['name'] ?? 'Unknown') ?></strong>
                                            <?php if (isset($officer['id'])): ?>
                                                <small class="text-muted">(ID: <?= $officer['id'] ?>)</small>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <span class="text-muted">No officers recorded</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <!-- Activities and Outcomes -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Achievements</h6>
                            
                            <?php
                            $achievements = '';
                            if (!empty($visit['achievements'])) {
                                if (is_string($visit['achievements'])) {
                                    $achievementsData = json_decode($visit['achievements'], true);
                                    if (is_array($achievementsData) && isset($achievementsData['description'])) {
                                        $achievements = $achievementsData['description'];
                                    } else {
                                        $achievements = $visit['achievements'];
                                    }
                                } else {
                                    $achievements = $visit['achievements'];
                                }
                            }
                            ?>
                            
                            <?php if (!empty($achievements)): ?>
                                <div class="bg-light p-3 rounded">
                                    <?= nl2br(esc($achievements)) ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">No achievements recorded</span>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">Beneficiaries</h6>
                            
                            <?php
                            $beneficiaries = '';
                            if (!empty($visit['beneficiaries'])) {
                                if (is_string($visit['beneficiaries'])) {
                                    $beneficiariesData = json_decode($visit['beneficiaries'], true);
                                    if (is_array($beneficiariesData) && isset($beneficiariesData['description'])) {
                                        $beneficiaries = $beneficiariesData['description'];
                                    } else {
                                        $beneficiaries = $visit['beneficiaries'];
                                    }
                                } else {
                                    $beneficiaries = $visit['beneficiaries'];
                                }
                            }
                            ?>
                            
                            <?php if (!empty($beneficiaries)): ?>
                                <div class="bg-light p-3 rounded">
                                    <?= nl2br(esc($beneficiaries)) ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">No beneficiaries information recorded</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
