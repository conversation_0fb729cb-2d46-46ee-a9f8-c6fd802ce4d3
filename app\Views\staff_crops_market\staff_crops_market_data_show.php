<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">View Crops Market Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/marketing_data') ?>">Crops Market Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">View Item</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Market Data Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Exercise ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['exercise_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Farmer ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['farmer_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Block ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['block_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Crop</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['crop_name']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Country ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['country_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Province ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['province_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">District ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['district_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">LLG ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['llg_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Market Date</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['market_date']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Market Stage</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['market_stage']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Buyer ID</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['buyer_id']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Selling Location</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['selling_location']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Product</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['product']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Product Type</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['product_type']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['description']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Unit of Measure</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['unit_of_measure']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Unit</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['unit']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantity</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['quantity']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Market Price Per Unit</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['market_price_per_unit']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total Freight Cost</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['total_freight_cost']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Remarks</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['remarks']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <span class="badge <?= $marketingData['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                            <?= esc(ucfirst($marketingData['status'])) ?>
                        </span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Created By</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['created_by']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Updated By</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['updated_by']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Created At</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['created_at']) ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Updated At</label>
                        <p class="form-control-plaintext"><?= esc($marketingData['updated_at']) ?></p>
                    </div>
                    <div class="d-flex justify-content-end">
                        <a href="<?= base_url('staff/farms/marketing_data/' . esc($marketingData['id']) . '/edit') ?>" class="btn btn-sm btn-warning me-2">
                            <i class="fas fa-edit me-2"></i>Edit
                        </a>
                        <a href="<?= base_url('staff/farms/marketing_data/' . esc($marketingData['id']) . '/delete') ?>" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
