<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\LivestockFarmDataModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\LivestockModel;
use App\Models\FarmerInformationModel;

class StaffLivestockDataController extends BaseController
{
    protected $livestockFarmDataModel;
    protected $livestockFarmBlockModel;
    protected $livestockModel;
    protected $farmerInformationModel;

    public function __construct()
    {
        $this->livestockFarmDataModel = new LivestockFarmDataModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->livestockModel = new LivestockModel();
        $this->farmerInformationModel = new FarmerInformationModel();
        
        // Load helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display a listing of livestock farm data for a specific block
     */
    public function index($block_id)
    {
        // Get block details
        $block = $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname,
                    adx_ward.name as ward_name, adx_llg.name as llg_name,
                    adx_district.name as district_name, adx_province.name as province_name')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
            ->where('livestock_farm_blocks.id', $block_id)
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to(base_url('staff/livestock-farm-blocks'))
                ->with('error', 'Livestock farm block not found.');
        }

        $data = [
            'title' => 'Livestock Farm Data - ' . $block['block_code'],
            'page_header' => 'Livestock Farm Data for Block ' . $block['block_code'],
            'block' => $block,
            'livestock_data' => $this->livestockFarmDataModel->getLivestockDataWithDetails([
                'livestock_farm_data.block_id' => $block_id,
                'livestock_farm_data.status' => 'active'
            ])
        ];

        return view('staff_livestock_data/staff_livestock_data_index', $data);
    }

    /**
     * Show the form for creating new livestock farm data for a specific block
     */
    public function create($block_id)
    {
        // Get block details
        $block = $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->where('livestock_farm_blocks.id', $block_id)
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to(base_url('staff/livestock-farm-blocks'))
                ->with('error', 'Livestock farm block not found.');
        }

        $data = [
            'title' => 'Add Livestock Farm Data - ' . $block['block_code'],
            'page_header' => 'Add Livestock Farm Data for Block ' . $block['block_code'],
            'block' => $block,
            'livestock_types' => $this->livestockModel->getLivestock()
        ];

        return view('staff_livestock_data/staff_livestock_data_create', $data);
    }

    /**
     * Store newly created livestock farm data for a specific block
     */
    public function store($block_id)
    {
        // Verify block exists and belongs to user's district
        $block = $this->livestockFarmBlockModel
            ->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to(base_url('staff/livestock-farm-blocks'))
                ->with('error', 'Livestock farm block not found.');
        }

        $validation = \Config\Services::validation();

        $rules = [
            'livestock_id' => 'required|numeric',
            'breed' => 'required|max_length[255]',
            'he_total' => 'permit_empty|numeric',
            'she_total' => 'permit_empty|numeric',
            'pasture_type' => 'permit_empty|max_length[100]',
            'growth_stage' => 'permit_empty|max_length[100]',
            'cost_per_livestock' => 'permit_empty|numeric',
            'low_price_per_livestock' => 'permit_empty|numeric',
            'high_price_per_livestock' => 'permit_empty|numeric',
            'action_date' => 'required|valid_date',
            'comments' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $validation->getErrors());
        }

        // Validate price range
        $lowPrice = $this->request->getPost('low_price_per_livestock');
        $highPrice = $this->request->getPost('high_price_per_livestock');

        if (!empty($lowPrice) && !empty($highPrice) && $lowPrice > $highPrice) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Lowest price cannot be greater than highest price.');
        }

        $data = [
            'exercise_id' => session()->get('exercise_id'),
            'block_id' => $block_id, // Use the block_id from URL
            'livestock_id' => $this->request->getPost('livestock_id'),
            'breed' => $this->request->getPost('breed'),
            'he_total' => $this->request->getPost('he_total') ?: 0,
            'she_total' => $this->request->getPost('she_total') ?: 0,
            'pasture_type' => $this->request->getPost('pasture_type'),
            'growth_stage' => $this->request->getPost('growth_stage'),
            'cost_per_livestock' => $this->request->getPost('cost_per_livestock') ?: 0,
            'low_price_per_livestock' => $this->request->getPost('low_price_per_livestock') ?: 0,
            'high_price_per_livestock' => $this->request->getPost('high_price_per_livestock') ?: 0,
            'action_date' => $this->request->getPost('action_date'),
            'comments' => $this->request->getPost('comments'),
            'created_by' => session()->get('emp_id'),
            'status' => 'active'
        ];

        try {
            $this->livestockFarmDataModel->insert($data);
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('success', 'Livestock farm data added successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Store Livestock Data] ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to add livestock farm data. Please try again.');
        }
    }

    /**
     * Display the specified livestock farm data
     */
    public function show($block_id, $id)
    {
        $livestockData = $this->livestockFarmDataModel
            ->select('
                livestock_farm_data.*,
                livestock_farm_blocks.block_code,
                livestock_farm_blocks.block_site,
                livestock_farm_blocks.village,
                adx_livestock.name as livestock_name,
                adx_livestock.color_code,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name,
                adx_province.name as province_name
            ')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_ward', 'adx_ward.id = livestock_farm_blocks.ward_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->join('adx_district', 'adx_district.id = livestock_farm_blocks.district_id')
            ->join('adx_province', 'adx_province.id = livestock_farm_blocks.province_id')
            ->where('livestock_farm_data.id', $id)
            ->where('livestock_farm_data.block_id', $block_id)
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->first();

        if (!$livestockData) {
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Livestock farm data not found.');
        }

        $data = [
            'title' => 'View Livestock Farm Data',
            'page_header' => 'View Livestock Farm Data',
            'livestock_data' => $livestockData,
            'block_id' => $block_id
        ];

        return view('staff_livestock_data/staff_livestock_data_show', $data);
    }

    /**
     * Show the form for editing livestock farm data
     */
    public function edit($block_id, $id)
    {
        $livestockData = $this->livestockFarmDataModel
            ->where('id', $id)
            ->where('block_id', $block_id)
            ->where('status', 'active')
            ->first();

        if (!$livestockData) {
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Livestock farm data not found.');
        }

        // Get block details
        $block = $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.*, farmer_information.given_name, farmer_information.surname')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->where('livestock_farm_blocks.id', $block_id)
            ->first();

        $data = [
            'title' => 'Edit Livestock Farm Data',
            'page_header' => 'Edit Livestock Farm Data',
            'livestock_data' => $livestockData,
            'block' => $block,
            'block_id' => $block_id,
            'livestock_types' => $this->livestockModel->getLivestock()
        ];

        return view('staff_livestock_data/staff_livestock_data_edit', $data);
    }

    /**
     * Update the specified livestock farm data
     */
    public function update($block_id, $id)
    {
        $livestockData = $this->livestockFarmDataModel
            ->where('id', $id)
            ->where('block_id', $block_id)
            ->where('status', 'active')
            ->first();

        if (!$livestockData) {
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Livestock farm data not found.');
        }

        $validation = \Config\Services::validation();

        $rules = [
            'livestock_id' => 'required|numeric',
            'breed' => 'required|max_length[255]',
            'he_total' => 'permit_empty|numeric',
            'she_total' => 'permit_empty|numeric',
            'pasture_type' => 'permit_empty|max_length[100]',
            'growth_stage' => 'permit_empty|max_length[100]',
            'cost_per_livestock' => 'permit_empty|numeric',
            'low_price_per_livestock' => 'permit_empty|numeric',
            'high_price_per_livestock' => 'permit_empty|numeric',
            'action_date' => 'required|valid_date',
            'comments' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $validation->getErrors());
        }

        // Validate price range
        $lowPrice = $this->request->getPost('low_price_per_livestock');
        $highPrice = $this->request->getPost('high_price_per_livestock');

        if (!empty($lowPrice) && !empty($highPrice) && $lowPrice > $highPrice) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Lowest price cannot be greater than highest price.');
        }

        $data = [
            'livestock_id' => $this->request->getPost('livestock_id'),
            'breed' => $this->request->getPost('breed'),
            'he_total' => $this->request->getPost('he_total') ?: 0,
            'she_total' => $this->request->getPost('she_total') ?: 0,
            'pasture_type' => $this->request->getPost('pasture_type'),
            'growth_stage' => $this->request->getPost('growth_stage'),
            'cost_per_livestock' => $this->request->getPost('cost_per_livestock') ?: 0,
            'low_price_per_livestock' => $this->request->getPost('low_price_per_livestock') ?: 0,
            'high_price_per_livestock' => $this->request->getPost('high_price_per_livestock') ?: 0,
            'action_date' => $this->request->getPost('action_date'),
            'comments' => $this->request->getPost('comments'),
            'updated_by' => session()->get('emp_id')
        ];

        try {
            $this->livestockFarmDataModel->update($id, $data);
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('success', 'Livestock farm data updated successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Update Livestock Data] ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update livestock farm data. Please try again.');
        }
    }

    /**
     * Remove the specified livestock farm data (soft delete)
     * Only allows deletion if data is less than 1 day old
     */
    public function destroy($block_id, $id)
    {
        $livestockData = $this->livestockFarmDataModel
            ->where('id', $id)
            ->where('block_id', $block_id)
            ->where('status', 'active')
            ->first();

        if (!$livestockData) {
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Livestock farm data not found.');
        }

        // Check if data is more than 1 day old
        $createdAt = new \DateTime($livestockData['created_at']);
        $now = new \DateTime();
        $diff = $now->diff($createdAt);
        $daysDiff = $diff->days;

        if ($daysDiff >= 1) {
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Cannot delete livestock data that is more than 1 day old. This record was created on ' .
                       date('d-m-Y H:i', strtotime($livestockData['created_at'])) . '.');
        }

        try {
            $this->livestockFarmDataModel->update($id, [
                'status' => 'deleted',
                'deleted_by' => session()->get('emp_id')
            ]);

            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('success', 'Livestock farm data deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Livestock Data] ' . $e->getMessage());
            return redirect()->to(base_url('staff/livestock/blocks/' . $block_id . '/data'))
                ->with('error', 'Failed to delete livestock farm data. Please try again.');
        }
    }
}
