<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Training Management</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="#">Extension Services</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Trainings</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/extension/trainings/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Training
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i>Training Records
                    </h5>
                    <p class="card-text mb-0 text-muted">Manage and track all training activities</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="trainingsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Topic</th>
                                    <th>Location</th>
                                    <th>Date Range</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($trainings)): ?>
                                    <?php foreach ($trainings as $training): ?>
                                        <?php
                                        // Status badges
                                        $statusBadges = [
                                            1 => '<span class="badge bg-success">Active</span>',
                                            2 => '<span class="badge bg-info">Completed</span>',
                                            3 => '<span class="badge bg-warning">Pending</span>',
                                            0 => '<span class="badge bg-secondary">Inactive</span>'
                                        ];
                                        $statusBadge = $statusBadges[$training['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                        ?>
                                        <tr>
                                            <td><strong><?= $training['id'] ?></strong></td>
                                            <td>
                                                <strong><?= esc($training['topic']) ?></strong>
                                                <br><small class="text-muted">Created: <?= date('d-M-Y', strtotime($training['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <?= esc($training['locations']) ?>
                                                <br><small class="text-muted"><?= esc($training['llg_name'] ?? 'N/A') ?></small>
                                            </td>
                                            <td>
                                                <strong><?= date('d-M-Y', strtotime($training['date_start'])) ?></strong>
                                                <br><small class="text-muted">to <?= date('d-M-Y', strtotime($training['date_end'])) ?></small>
                                            </td>
                                            <td><?= $statusBadge ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?= base_url('staff/extension/trainings/' . $training['id']) ?>" 
                                                       class="btn btn-sm btn-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/extension/trainings/' . $training['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-warning" title="Edit Training">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="post" action="<?= base_url('staff/extension/trainings/' . $training['id'] . '/delete') ?>" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to delete this training?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete Training">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>No training records found
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#trainingsTable').DataTable({
        responsive: true,
        order: [[0, "desc"]],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on Actions column
        ],
        language: {
            search: "Search trainings:",
            lengthMenu: "Show _MENU_ trainings per page",
            info: "Showing _START_ to _END_ of _TOTAL_ trainings",
            infoEmpty: "No trainings found",
            infoFiltered: "(filtered from _MAX_ total trainings)"
        }
    });
});
</script>
<?= $this->endSection() ?>
