<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff') ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff/farms/fertilizer_data') ?>">Fertilizer Data</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff/farms/fertilizer_data/show/' . $fertilizer_data['block_id']) ?>"><?= esc($fertilizer_data['block_code']) ?></a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Edit Fertilizer Data</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/farms/fertilizer_data/show/' . $fertilizer_data['block_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Fertilizer Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Block Information Card -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Farm Block Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Block Code:</strong><br>
                            <span class="text-primary"><?= esc($fertilizer_data['block_code']) ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Farmer:</strong><br>
                            <?= esc($fertilizer_data['given_name']) ?> <?= esc($fertilizer_data['surname']) ?><br>
                            <small class="text-muted">Code: <?= esc($fertilizer_data['farmer_code']) ?></small>
                        </div>
                        <div class="col-md-3">
                            <strong>Crop:</strong><br>
                            <span class="badge bg-success"><?= esc($fertilizer_data['crop_name']) ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Application Date:</strong><br>
                            <?= date('M d, Y', strtotime($fertilizer_data['action_date'])) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Fertilizer Data Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Fertilizer Application
                    </h5>
                    <p class="card-text mb-0 text-muted">
                        Update the details of the fertilizer application
                    </p>
                </div>
                <div class="card-body">
                    <!-- Error Messages -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Please correct the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Form -->
                    <?= form_open('staff/farms/fertilizer_data/update/' . $fertilizer_data['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>

                    <div class="row">
                        <!-- Fertilizer Type -->
                        <div class="col-md-6 mb-3">
                            <label for="fertilizer_id" class="form-label">Fertilizer Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="fertilizer_id" name="fertilizer_id" required>
                                <option value="">Select Fertilizer Type</option>
                                <?php foreach ($fertilizers as $fertilizer): ?>
                                    <option value="<?= esc($fertilizer['id']) ?>" 
                                            <?= (old('fertilizer_id', $fertilizer_data['fertilizer_id']) == $fertilizer['id']) ? 'selected' : '' ?>>
                                        <?= esc($fertilizer['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a fertilizer type.
                            </div>
                        </div>

                        <!-- Application Date -->
                        <div class="col-md-6 mb-3">
                            <label for="action_date" class="form-label">Application Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="action_date" name="action_date" 
                                   value="<?= old('action_date', $fertilizer_data['action_date']) ?>" required>
                            <div class="invalid-feedback">
                                Please provide a valid application date.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Fertilizer Name -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Fertilizer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= old('name', $fertilizer_data['name']) ?>" placeholder="Enter fertilizer name" required>
                            <div class="invalid-feedback">
                                Please provide the fertilizer name.
                            </div>
                        </div>

                        <!-- Brand -->
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label">Brand <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="brand" name="brand" 
                                   value="<?= old('brand', $fertilizer_data['brand']) ?>" placeholder="Enter brand name" required>
                            <div class="invalid-feedback">
                                Please provide the brand name.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Unit -->
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="unit" name="unit" 
                                   value="<?= old('unit', $fertilizer_data['unit']) ?>" placeholder="Enter unit value" step="0.01" min="0.01" required>
                            <div class="invalid-feedback">
                                Please provide a valid unit value.
                            </div>
                        </div>

                        <!-- Unit of Measure -->
                        <div class="col-md-4 mb-3">
                            <label for="unit_of_measure" class="form-label">Unit of Measure <span class="text-danger">*</span></label>
                            <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                <option value="">Select Unit</option>
                                <option value="kg" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'kg' ? 'selected' : '' ?>>Kilograms (kg)</option>
                                <option value="g" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'g' ? 'selected' : '' ?>>Grams (g)</option>
                                <option value="lbs" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'lbs' ? 'selected' : '' ?>>Pounds (lbs)</option>
                                <option value="bags" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'bags' ? 'selected' : '' ?>>Bags</option>
                                <option value="liters" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'liters' ? 'selected' : '' ?>>Liters</option>
                                <option value="ml" <?= old('unit_of_measure', $fertilizer_data['unit_of_measure']) == 'ml' ? 'selected' : '' ?>>Milliliters (ml)</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a unit of measure.
                            </div>
                        </div>

                        <!-- Quantity -->
                        <div class="col-md-4 mb-3">
                            <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   value="<?= old('quantity', $fertilizer_data['quantity']) ?>" placeholder="Enter quantity" step="0.01" min="0.01" required>
                            <div class="invalid-feedback">
                                Please provide a valid quantity.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Remarks -->
                        <div class="col-12 mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                      placeholder="Enter any additional notes or remarks"><?= old('remarks', $fertilizer_data['remarks']) ?></textarea>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="<?= base_url('staff/farms/fertilizer_data/show/' . $fertilizer_data['block_id']) ?>" 
                                   class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Fertilizer Data
                                </button>
                            </div>
                        </div>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
<?= $this->endSection() ?>
