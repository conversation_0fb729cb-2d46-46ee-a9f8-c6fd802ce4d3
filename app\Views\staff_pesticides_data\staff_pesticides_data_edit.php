<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms') ?>">Farms</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/pesticides_data') ?>">Pesticides Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/pesticides_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/farms/pesticides_data/' . $pesticide_data['id']) ?>" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Please correct the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Pesticides Data
                    </h5>
                    <p class="card-text mb-0 text-muted">Update pesticide usage information for farm blocks</p>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('staff/farms/pesticides_data/' . $pesticide_data['id']) ?>" method="post" id="pesticidesDataForm">
                        <?= csrf_field() ?>
                        <input type="hidden" name="_method" value="PUT">
                        
                        <div class="row">
                            <!-- Farm Block Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="block_id" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Farm Block <span class="text-danger">*</span>
                                </label>
                                <select class="form-select select2" id="block_id" name="block_id" required>
                                    <option value="">Select Farm Block</option>
                                    <?php foreach ($farm_blocks as $block): ?>
                                        <option value="<?= $block['id'] ?>" 
                                                data-crop="<?= esc($block['crop_name']) ?>"
                                                data-farmer="<?= esc($block['farmer_name']) ?>"
                                                <?= (old('block_id', $pesticide_data['block_id']) == $block['id']) ? 'selected' : '' ?>>
                                            <?= esc($block['block_code']) ?> - <?= esc($block['farmer_name']) ?> (<?= esc($block['crop_name']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select the farm block where pesticide was applied</div>
                            </div>

                            <!-- Pesticide Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="pesticide_id" class="form-label">
                                    <i class="fas fa-bug me-1"></i>Registered Pesticide
                                </label>
                                <select class="form-select select2" id="pesticide_id" name="pesticide_id">
                                    <option value="">Select Registered Pesticide (Optional)</option>
                                    <?php foreach ($pesticides as $pesticide): ?>
                                        <option value="<?= $pesticide['id'] ?>" 
                                                <?= (old('pesticide_id', $pesticide_data['pesticide_id']) == $pesticide['id']) ? 'selected' : '' ?>>
                                            <?= esc($pesticide['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select from registered pesticides or fill custom name below</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Pesticide Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Pesticide Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $pesticide_data['name']) ?>" required maxlength="100">
                                <div class="form-text">Commercial or common name of the pesticide</div>
                            </div>

                            <!-- Brand -->
                            <div class="col-md-6 mb-3">
                                <label for="brand" class="form-label">
                                    <i class="fas fa-copyright me-1"></i>Brand
                                </label>
                                <input type="text" class="form-control" id="brand" name="brand" 
                                       value="<?= old('brand', $pesticide_data['brand']) ?>" maxlength="100">
                                <div class="form-text">Brand name or manufacturer</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Unit of Measure -->
                            <div class="col-md-4 mb-3">
                                <label for="unit_of_measure" class="form-label">
                                    <i class="fas fa-ruler me-1"></i>Unit of Measure <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                    <option value="">Select Unit</option>
                                    <option value="Weight" <?= (old('unit_of_measure', $pesticide_data['unit_of_measure']) == 'Weight') ? 'selected' : '' ?>>Weight</option>
                                    <option value="Volume" <?= (old('unit_of_measure', $pesticide_data['unit_of_measure']) == 'Volume') ? 'selected' : '' ?>>Volume</option>
                                    <option value="Area" <?= (old('unit_of_measure', $pesticide_data['unit_of_measure']) == 'Area') ? 'selected' : '' ?>>Area</option>
                                    <option value="Count" <?= (old('unit_of_measure', $pesticide_data['unit_of_measure']) == 'Count') ? 'selected' : '' ?>>Count</option>
                                </select>
                            </div>

                            <!-- Unit -->
                            <div class="col-md-4 mb-3">
                                <label for="unit" class="form-label">
                                    <i class="fas fa-balance-scale me-1"></i>Unit <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="unit" name="unit" required>
                                    <option value="">Select Unit</option>
                                    <!-- Weight units -->
                                    <optgroup label="Weight">
                                        <option value="kg" <?= (old('unit', $pesticide_data['unit']) == 'kg') ? 'selected' : '' ?>>Kilograms (kg)</option>
                                        <option value="g" <?= (old('unit', $pesticide_data['unit']) == 'g') ? 'selected' : '' ?>>Grams (g)</option>
                                        <option value="lbs" <?= (old('unit', $pesticide_data['unit']) == 'lbs') ? 'selected' : '' ?>>Pounds (lbs)</option>
                                    </optgroup>
                                    <!-- Volume units -->
                                    <optgroup label="Volume">
                                        <option value="L" <?= (old('unit', $pesticide_data['unit']) == 'L') ? 'selected' : '' ?>>Liters (L)</option>
                                        <option value="mL" <?= (old('unit', $pesticide_data['unit']) == 'mL') ? 'selected' : '' ?>>Milliliters (mL)</option>
                                        <option value="gal" <?= (old('unit', $pesticide_data['unit']) == 'gal') ? 'selected' : '' ?>>Gallons (gal)</option>
                                    </optgroup>
                                    <!-- Area units -->
                                    <optgroup label="Area">
                                        <option value="ha" <?= (old('unit', $pesticide_data['unit']) == 'ha') ? 'selected' : '' ?>>Hectares (ha)</option>
                                        <option value="m²" <?= (old('unit', $pesticide_data['unit']) == 'm²') ? 'selected' : '' ?>>Square meters (m²)</option>
                                        <option value="acres" <?= (old('unit', $pesticide_data['unit']) == 'acres') ? 'selected' : '' ?>>Acres</option>
                                    </optgroup>
                                    <!-- Count units -->
                                    <optgroup label="Count">
                                        <option value="pcs" <?= (old('unit', $pesticide_data['unit']) == 'pcs') ? 'selected' : '' ?>>Pieces (pcs)</option>
                                        <option value="bottles" <?= (old('unit', $pesticide_data['unit']) == 'bottles') ? 'selected' : '' ?>>Bottles</option>
                                        <option value="packets" <?= (old('unit', $pesticide_data['unit']) == 'packets') ? 'selected' : '' ?>>Packets</option>
                                    </optgroup>
                                </select>
                            </div>

                            <!-- Quantity -->
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-calculator me-1"></i>Quantity <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       value="<?= old('quantity', $pesticide_data['quantity']) ?>" required step="0.01" min="0">
                                <div class="form-text">Amount of pesticide used</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Action Date -->
                            <div class="col-md-6 mb-3">
                                <label for="action_date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Application Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="action_date" name="action_date" 
                                       value="<?= old('action_date', $pesticide_data['action_date']) ?>" required>
                                <div class="form-text">Date when pesticide was applied</div>
                            </div>

                            <!-- Record Info Display -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Record Information
                                </label>
                                <div class="form-control-plaintext bg-light rounded p-2">
                                    <small>
                                        <strong>Created:</strong> <?= date('M d, Y H:i', strtotime($pesticide_data['created_at'])) ?><br>
                                        <?php if ($pesticide_data['updated_at'] && $pesticide_data['updated_at'] !== $pesticide_data['created_at']): ?>
                                            <strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($pesticide_data['updated_at'])) ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Remarks -->
                        <div class="mb-3">
                            <label for="remarks" class="form-label">
                                <i class="fas fa-comment me-1"></i>Remarks
                            </label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"><?= old('remarks', $pesticide_data['remarks']) ?></textarea>
                            <div class="form-text">Additional notes about the pesticide application</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('staff/farms/pesticides_data') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Pesticides Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Auto-fill pesticide name when registered pesticide is selected
    $('#pesticide_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        if (selectedOption.val() && selectedOption.text() !== 'Select Registered Pesticide (Optional)') {
            $('#name').val(selectedOption.text());
        }
    });

    // Form validation
    $('#pesticidesDataForm').on('submit', function(e) {
        const quantity = parseFloat($('#quantity').val());
        if (quantity <= 0) {
            e.preventDefault();
            alert('Quantity must be greater than 0');
            $('#quantity').focus();
            return false;
        }
    });
});
</script>

<?= $this->endSection() ?> 