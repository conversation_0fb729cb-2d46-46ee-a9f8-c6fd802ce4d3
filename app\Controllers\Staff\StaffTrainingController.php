<?php

namespace App\Controllers\Staff;

use App\Models\TrainingsModel;
use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use CodeIgniter\RESTful\ResourceController;

class StaffTrainingController extends ResourceController
{
    protected $trainingsModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $helpers = ['form', 'url', 'file', 'text', 'info'];

    public function __construct()
    {
        // Load helpers
        helper(['form', 'url', 'file', 'text', 'info']);

        $this->trainingsModel = new TrainingsModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
    }

    /**
     * Display a list of all trainings
     */
    public function index()
    {
        // Get all trainings with location information
        $trainings = $this->trainingsModel
            ->select('trainings.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = trainings.country_id', 'left')
            ->join('adx_province p', 'p.id = trainings.province_id', 'left')
            ->join('adx_district d', 'd.id = trainings.district_id', 'left')
            ->join('adx_llg l', 'l.id = trainings.llg_id', 'left')
            ->where('trainings.deleted_at IS NULL')
            ->orderBy('trainings.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Training Management',
            'trainings' => $trainings
        ];

        return view('staff_trainings/staff_trainings_index', $data);
    }

    /**
     * Display the form for creating a new training
     */
    public function create()
    {
        // Get location data from session
        $districtId = session()->get('district_id');
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Create Training',
            'llgs' => $llgs
        ];

        return view('staff_trainings/staff_trainings_create', $data);
    }

    /**
     * Store a newly created training
     */
    public function store()
    {
        // Validation rules based on TrainingsModel
        $rules = [
            'llg_id' => 'required|numeric',
            'locations' => 'required',
            'gps' => 'required',
            'date_start' => 'required|valid_date',
            'date_end' => 'required|valid_date',
            'topic' => 'required|min_length[3]|max_length[255]',
            'objectives' => 'required',
            'status' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Get location data from session
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Process trainers (JSON array)
        $trainersInput = $this->request->getPost('trainers');
        $trainers = !empty($trainersInput) ? json_encode(array_filter(explode("\n", trim($trainersInput)))) : null;

        // Process attendees (JSON array)
        $attendeesInput = $this->request->getPost('attendees');
        $attendees = !empty($attendeesInput) ? json_encode(array_filter(explode("\n", trim($attendeesInput)))) : null;

        // Prepare data for insertion
        $data = [
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $this->request->getPost('locations'),
            'gps' => $this->request->getPost('gps'),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'topic' => $this->request->getPost('topic'),
            'objectives' => $this->request->getPost('objectives'),
            'content' => $this->request->getPost('content'),
            'trainers' => $trainers,
            'attendees' => $attendees,
            'materials' => $this->request->getPost('materials'),
            'status' => $this->request->getPost('status'),
            'created_by' => session()->get('emp_id') ?? 1
        ];

        // Insert the training
        if ($this->trainingsModel->insert($data)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training created successfully');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to create training');
        }
    }

    /**
     * Display the specified training
     */
    public function show($id = null)
    {
        $training = $this->trainingsModel
            ->select('trainings.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = trainings.country_id', 'left')
            ->join('adx_province p', 'p.id = trainings.province_id', 'left')
            ->join('adx_district d', 'd.id = trainings.district_id', 'left')
            ->join('adx_llg l', 'l.id = trainings.llg_id', 'left')
            ->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        $data = [
            'title' => 'Training Details',
            'training' => $training
        ];

        return view('staff_trainings/staff_trainings_view', $data);
    }

    /**
     * Display the form for editing the specified training
     */
    public function edit($id = null)
    {
        $training = $this->trainingsModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Get location data from session
        $districtId = session()->get('district_id');
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Edit Training',
            'training' => $training,
            'llgs' => $llgs
        ];

        return view('staff_trainings/staff_trainings_edit', $data);
    }

    /**
     * Update the specified training
     */
    public function update($id = null)
    {
        $training = $this->trainingsModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Validation rules
        $rules = [
            'llg_id' => 'required|numeric',
            'locations' => 'required',
            'gps' => 'required',
            'date_start' => 'required|valid_date',
            'date_end' => 'required|valid_date',
            'topic' => 'required|min_length[3]|max_length[255]',
            'objectives' => 'required',
            'status' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Process trainers (JSON array)
        $trainersInput = $this->request->getPost('trainers');
        $trainers = !empty($trainersInput) ? json_encode(array_filter(explode("\n", trim($trainersInput)))) : null;

        // Process attendees (JSON array)
        $attendeesInput = $this->request->getPost('attendees');
        $attendees = !empty($attendeesInput) ? json_encode(array_filter(explode("\n", trim($attendeesInput)))) : null;

        // Prepare data for update
        $data = [
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $this->request->getPost('locations'),
            'gps' => $this->request->getPost('gps'),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'topic' => $this->request->getPost('topic'),
            'objectives' => $this->request->getPost('objectives'),
            'content' => $this->request->getPost('content'),
            'trainers' => $trainers,
            'attendees' => $attendees,
            'materials' => $this->request->getPost('materials'),
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('emp_id') ?? 1
        ];

        // Update the training
        if ($this->trainingsModel->update($id, $data)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training updated successfully');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to update training');
        }
    }

    /**
     * Delete the specified training
     */
    public function destroy($id = null)
    {
        $training = $this->trainingsModel->find($id);

        if (!$training) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Training not found');
        }

        // Perform soft delete
        if ($this->trainingsModel->delete($id)) {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('success', 'Training deleted successfully');
        } else {
            return redirect()->to(base_url('staff/extension/trainings'))
                ->with('error', 'Failed to delete training');
        }
    }
}
