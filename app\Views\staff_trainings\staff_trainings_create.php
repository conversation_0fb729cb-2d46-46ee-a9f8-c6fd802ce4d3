<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Create New Training</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/trainings') ?>">Trainings</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Create</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Training Information
                    </h5>
                    <p class="card-text mb-0 text-muted">Fill in the details for the new training</p>
                </div>
                <div class="card-body">
                    <!-- Error Messages -->
                    <?php if (session()->has('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Training Form -->
                    <?= form_open('staff/extension/trainings', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="topic" class="form-label">Training Topic <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="topic" name="topic" 
                                           value="<?= old('topic') ?>" required>
                                    <div class="invalid-feedback">Please provide a training topic.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                    <select class="form-select" id="llg_id" name="llg_id" required>
                                        <option value="">Select LLG</option>
                                        <?php foreach ($llgs as $llg): ?>
                                            <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                                <?= esc($llg['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">Please select an LLG.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="locations" class="form-label">Training Locations <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="locations" name="locations" 
                                           value="<?= old('locations') ?>" required
                                           placeholder="e.g., Community Hall, Village Center">
                                    <div class="invalid-feedback">Please provide training locations.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="gps" class="form-label">GPS Coordinates <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="gps" name="gps" 
                                           value="<?= old('gps') ?>" required
                                           placeholder="e.g., -6.314993, 143.95555">
                                    <div class="invalid-feedback">Please provide GPS coordinates.</div>
                                </div>
                            </div>

                            <!-- Dates and Status -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="date_start" name="date_start" 
                                           value="<?= old('date_start') ?>" required>
                                    <div class="invalid-feedback">Please provide a start date.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="date_end" class="form-label">End Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="date_end" name="date_end" 
                                           value="<?= old('date_end') ?>" required>
                                    <div class="invalid-feedback">Please provide an end date.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="3" <?= old('status') == '3' ? 'selected' : '' ?>>Pending</option>
                                        <option value="1" <?= old('status') == '1' ? 'selected' : '' ?>>Active</option>
                                        <option value="2" <?= old('status') == '2' ? 'selected' : '' ?>>Completed</option>
                                        <option value="0" <?= old('status') == '0' ? 'selected' : '' ?>>Inactive</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a status.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Training Details -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="objectives" class="form-label">Training Objectives <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="objectives" name="objectives" rows="3" required
                                              placeholder="Describe the main objectives of this training..."><?= old('objectives') ?></textarea>
                                    <div class="invalid-feedback">Please provide training objectives.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="content" class="form-label">Training Content</label>
                                    <textarea class="form-control" id="content" name="content" rows="4"
                                              placeholder="Describe the training content and curriculum..."><?= old('content') ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="materials" class="form-label">Training Materials</label>
                                    <textarea class="form-control" id="materials" name="materials" rows="3"
                                              placeholder="List materials, resources, and equipment used..."><?= old('materials') ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Trainers and Attendees -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trainers" class="form-label">Trainers</label>
                                    <textarea class="form-control" id="trainers" name="trainers" rows="4"
                                              placeholder="List trainers (one per line)&#10;John Doe&#10;Jane Smith"><?= old('trainers') ?></textarea>
                                    <small class="form-text text-muted">Enter one trainer per line</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="attendees" class="form-label">Attendees</label>
                                    <textarea class="form-control" id="attendees" name="attendees" rows="4"
                                              placeholder="List attendees (one per line)&#10;Farmer 1&#10;Farmer 2"><?= old('attendees') ?></textarea>
                                    <small class="form-text text-muted">Enter one attendee per line</small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Create Training
                                    </button>
                                </div>
                            </div>
                        </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Date validation
    $('#date_start, #date_end').on('change', function() {
        var startDate = $('#date_start').val();
        var endDate = $('#date_end').val();
        
        if (startDate && endDate && startDate > endDate) {
            alert('End date must be after start date');
            $('#date_end').val('');
        }
    });
});
</script>
<?= $this->endSection() ?>
