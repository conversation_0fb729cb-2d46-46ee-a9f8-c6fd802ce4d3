<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff') ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff/crops-farm-blocks') ?>">Crops Farm Blocks</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Fertilizer Data</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-seedling me-2"></i>Farm Blocks - Fertilizer Data Management
                    </h5>
                    <p class="card-text mb-0 text-muted">
                        Select a farm block to view and manage fertilizer application data for <?= esc($district_name) ?>
                    </p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Farm Blocks Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="fertilizerFarmBlocksTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Block Code</th>
                                    <th>Farmer</th>
                                    <th>Crop</th>
                                    <th>Location</th>
                                    <th>Block Site</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($farm_blocks)): ?>
                                    <?php foreach ($farm_blocks as $block): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($block['block_code']) ?></strong>
                                            </td>
                                            <td>
                                                <strong><?= esc($block['given_name']) ?> <?= esc($block['surname']) ?></strong>
                                                <br>
                                                <small class="text-muted">Code: <?= esc($block['farmer_code']) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?= esc($block['crop_name']) ?></span>
                                            </td>
                                            <td>
                                                <?= esc($block['district_name']) ?><br>
                                                <small class="text-muted">
                                                    <?= esc($block['llg_name']) ?> / <?= esc($block['ward_name']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?= esc($block['block_site']) ?>
                                            </td>
                                            <td>
                                                <?php if ($block['status'] === 'active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?= ucfirst(esc($block['status'])) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/farms/fertilizer_data/show/' . $block['id']) ?>"
                                                       class="btn btn-sm btn-info" title="View Fertilizer Data">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/farms/fertilizer_data/create/' . $block['id']) ?>"
                                                       class="btn btn-sm btn-success" title="Add Fertilizer Data">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <?php if (empty($farm_blocks)): ?>
                            <tfoot>
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No farm blocks found for your district.
                                    </td>
                                </tr>
                            </tfoot>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Prevent template JS from interfering with form submissions
    $('form').addClass('fertilizer-form');

    // Wait for all scripts to load and DOM to be fully ready
    setTimeout(function() {
        try {
            var tableId = '#fertilizerFarmBlocksTable';
            var $table = $(tableId);

            // Check if table exists and has proper structure
            if ($table.length && $table.find('thead th').length > 0) {
                // Count header columns
                var headerColumnCount = $table.find('thead th').length;
                console.log('Header columns:', headerColumnCount);

                // Check if table has data rows or empty state
                var $tbody = $table.find('tbody');
                var hasData = $tbody.find('tr').length > 0 && $tbody.find('tr td[colspan]').length === 0;
                console.log('Has data rows:', hasData);

                // If table is empty, ensure proper structure
                if (!hasData) {
                    console.log('Table is empty, ensuring proper structure');
                    var emptyRow = $tbody.find('tr td[colspan]').parent();
                    if (emptyRow.length > 0) {
                        // Verify colspan matches header count
                        var colspanValue = emptyRow.find('td').attr('colspan');
                        if (parseInt(colspanValue) !== headerColumnCount) {
                            console.warn('Colspan mismatch: ' + colspanValue + ' vs ' + headerColumnCount);
                            emptyRow.find('td').attr('colspan', headerColumnCount);
                        }
                    }
                }

                // Destroy any existing DataTable instance
                if ($.fn.DataTable.isDataTable(tableId)) {
                    $table.DataTable().destroy();
                }

                // Initialize DataTable with error handling
                var dataTable = $table.DataTable({
                    responsive: true,
                    order: hasData ? [[0, 'asc']] : [], // Only set order if there's data
                    pageLength: 25,
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable sorting on Actions column
                    ],
                    language: {
                        search: "Search Farm Blocks:",
                        lengthMenu: "Show _MENU_ farm blocks per page",
                        info: "Showing _START_ to _END_ of _TOTAL_ farm blocks",
                        infoEmpty: "No farm blocks found",
                        infoFiltered: "(filtered from _MAX_ total farm blocks)",
                        emptyTable: "No farm blocks found for your district"
                    },
                    // Handle empty table gracefully
                    initComplete: function() {
                        console.log('Fertilizer Farm Blocks DataTable initialized successfully');
                    },
                    // Additional options for better empty table handling
                    deferRender: true,
                    processing: false
                });

                // Handle DataTable errors
                dataTable.on('error.dt', function(e, settings, techNote, message) {
                    console.error('DataTable error:', message);
                    console.error('Technical note:', techNote);
                });

            } else {
                console.warn('Fertilizer Farm Blocks table not found or has no headers');
            }
        } catch (error) {
            console.error('Error initializing Fertilizer Farm Blocks DataTable:', error);
            console.error('Error stack:', error.stack);
        }
    }, 300); // Increased delay further to ensure all template scripts are loaded
});
</script>
<?= $this->endSection() ?>
