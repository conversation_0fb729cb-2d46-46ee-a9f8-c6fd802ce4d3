<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Edit Crops Market Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/marketing_data') ?>">Crops Market Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit Item</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Market Data</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('staff/farms/marketing_data/' . esc($marketingData['id'])) ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="_method" value="put">
                        <div class="mb-3">
                            <label for="exercise_id" class="form-label">Exercise ID</label>
                            <input type="text" class="form-control" id="exercise_id" name="exercise_id" value="<?= esc($marketingData['exercise_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="farmer_id" class="form-label">Farmer ID</label>
                            <input type="text" class="form-control" id="farmer_id" name="farmer_id" value="<?= esc($marketingData['farmer_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="block_id" class="form-label">Block ID</label>
                            <input type="text" class="form-control" id="block_id" name="block_id" value="<?= esc($marketingData['block_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="crop_id" class="form-label">Crop</label>
                            <select class="form-select" id="crop_id" name="crop_id" required>
                                <?php foreach ($crops as $crop): ?>
                                    <option value="<?= esc($crop['id']) ?>" <?= $crop['id'] == $marketingData['crop_id'] ? 'selected' : '' ?>>
                                        <?= esc($crop['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="country_id" class="form-label">Country ID</label>
                            <input type="text" class="form-control" id="country_id" name="country_id" value="<?= esc($marketingData['country_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="province_id" class="form-label">Province ID</label>
                            <input type="text" class="form-control" id="province_id" name="province_id" value="<?= esc($marketingData['province_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="district_id" class="form-label">District ID</label>
                            <input type="text" class="form-control" id="district_id" name="district_id" value="<?= esc($marketingData['district_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="llg_id" class="form-label">LLG ID</label>
                            <input type="text" class="form-control" id="llg_id" name="llg_id" value="<?= esc($marketingData['llg_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="market_date" class="form-label">Market Date</label>
                            <input type="date" class="form-control" id="market_date" name="market_date" value="<?= esc($marketingData['market_date']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="market_stage" class="form-label">Market Stage</label>
                            <input type="text" class="form-control" id="market_stage" name="market_stage" value="<?= esc($marketingData['market_stage']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="buyer_id" class="form-label">Buyer ID</label>
                            <input type="text" class="form-control" id="buyer_id" name="buyer_id" value="<?= esc($marketingData['buyer_id']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="selling_location" class="form-label">Selling Location</label>
                            <input type="text" class="form-control" id="selling_location" name="selling_location" value="<?= esc($marketingData['selling_location']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="product" class="form-label">Product</label>
                            <input type="text" class="form-control" id="product" name="product" value="<?= esc($marketingData['product']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="product_type" class="form-label">Product Type</label>
                            <input type="text" class="form-control" id="product_type" name="product_type" value="<?= esc($marketingData['product_type']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" required><?= esc($marketingData['description']) ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="unit_of_measure" class="form-label">Unit of Measure</label>
                            <input type="text" class="form-control" id="unit_of_measure" name="unit_of_measure" value="<?= esc($marketingData['unit_of_measure']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="unit" class="form-label">Unit</label>
                            <input type="text" class="form-control" id="unit" name="unit" value="<?= esc($marketingData['unit']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" value="<?= esc($marketingData['quantity']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="market_price_per_unit" class="form-label">Market Price Per Unit</label>
                            <input type="number" class="form-control" id="market_price_per_unit" name="market_price_per_unit" value="<?= esc($marketingData['market_price_per_unit']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="total_freight_cost" class="form-label">Total Freight Cost</label>
                            <input type="number" class="form-control" id="total_freight_cost" name="total_freight_cost" value="<?= esc($marketingData['total_freight_cost']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks"><?= esc($marketingData['remarks']) ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active" <?= $marketingData['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= $marketingData['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
