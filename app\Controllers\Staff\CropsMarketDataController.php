<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsModel;
use App\Models\FarmerInformationModel;
use App\Models\CropBuyersModel;
use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;

class CropsMarketDataController extends BaseController
{
    protected $cropsFarmMarketingDataModel;
    protected $cropsModel;
    protected $farmerInformationModel;
    protected $cropBuyersModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;

    public function __construct()
    {
        // Check authentication and role
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
        $this->farmerInformationModel = new FarmerInformationModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
    }

    // Index method to display all crops market data
    public function index()
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->getMarketingReportData();
        return view('staff_crops_market/staff_crops_market_data_index', $data);
    }

    // Create method to display the form for adding new crops market data
    public function create()
    {
        $districtId = session()->get('district_id');

        // Get farmers in the user's district
        $farmers = $this->farmerInformationModel->select('id, farmer_code, given_name, surname')
            ->where('district_id', $districtId)
            ->where('status', 'active')
            ->orderBy('farmer_code', 'ASC')
            ->findAll();

        // Get crop buyers
        $buyers = $this->cropBuyersModel->select('id, name, buyer_code')
            ->where('status', 'active')
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get crops
        $crops = $this->cropsModel->select('id, crop_name')
            ->orderBy('crop_name', 'ASC')
            ->findAll();

        // Get LLGs in the user's district
        $llgs = $this->llgModel->select('id, name, llgcode')
            ->where('district_id', $districtId)
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get location data from session active district
        $countryId = session()->get('country_id');

        // Get district and province info from active district
        $district = $this->districtModel->select('adx_district.*, adx_province.name as province_name')
            ->join('adx_province', 'adx_province.id = adx_district.province_id')
            ->find($districtId);

        $provinceName = $district['province_name'] ?? 'Unknown Province';
        $districtName = $district['name'] ?? 'Unknown District';

        $data = [
            'title' => 'Add Marketing Data',
            'page_header' => 'Add New Marketing Data',
            'farmers' => $farmers,
            'buyers' => $buyers,
            'crops' => $crops,
            'llgs' => $llgs,
            'country_id' => $countryId,
            'province_id' => $district['province_id'] ?? null,
            'district_id' => $districtId,
            'province_name' => $provinceName,
            'district_name' => $districtName
        ];

        return view('staff_crops_market/staff_crops_market_data_create', $data);
    }

    // Store method to handle form submission for adding new crops market data
    public function store()
    {
        // Validation rules
        $rules = [
            'farmer_id' => 'required|numeric',
            'buyer_id' => 'required|numeric',
            'crop_id' => 'required|numeric',
            'market_date' => 'required|valid_date',
            'market_stage' => 'required|max_length[100]',
            'selling_location' => 'required|max_length[200]',
            'product' => 'required|max_length[255]',
            'product_type' => 'required|max_length[100]',
            'unit_of_measure' => 'required|max_length[50]',
            'unit' => 'required|numeric|greater_than[0]',
            'quantity' => 'required|numeric|greater_than[0]',
            'market_price_per_unit' => 'required|numeric|greater_than[0]',
            'total_freight_cost' => 'permit_empty|numeric'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data with automatic location IDs
        $data = [
            // Remove exercise_id and block_id as requested
            'farmer_id' => $this->request->getPost('farmer_id'),
            'buyer_id' => $this->request->getPost('buyer_id'),
            'crop_id' => $this->request->getPost('crop_id'),

            // Automatically insert location IDs from session
            'country_id' => session()->get('country_id'),
            'province_id' => session()->get('province_id'),
            'district_id' => session()->get('district_id'),
            'llg_id' => $this->request->getPost('llg_id'), // Keep this as user input

            // Market details
            'market_date' => $this->request->getPost('market_date'),
            'market_stage' => $this->request->getPost('market_stage'),
            'selling_location' => $this->request->getPost('selling_location'),

            // Product details
            'product' => $this->request->getPost('product'),
            'product_type' => $this->request->getPost('product_type'),
            'description' => $this->request->getPost('description'),

            // Quantity and pricing
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'market_price_per_unit' => $this->request->getPost('market_price_per_unit'),
            'total_freight_cost' => $this->request->getPost('total_freight_cost') ?: 0,

            // Additional info
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active',
            'created_by' => session()->get('emp_id')
        ];

        $insertId = $this->cropsFarmMarketingDataModel->insert($data);

        if ($insertId) {
            session()->setFlashdata('success', 'Marketing data added successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to add marketing data. Please try again.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }

    // Show method to display a single crops market data record
    public function show($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        return view('staff_crops_market/staff_crops_market_data_show', $data);
    }

    // Edit method to display the form for editing a crops market data record
    public function edit($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        $data['crops'] = $this->cropsModel->getActiveCrops();
        return view('staff_crops_market/staff_crops_market_data_edit', $data);
    }

    // Update method to handle form submission for updating a crops market data record
    public function update($id)
    {
        $postData = $this->request->getPost();
        $updateResult = $this->cropsFarmMarketingDataModel->update($id, $postData);

        if ($updateResult) {
            session()->setFlashdata('success', 'Crops market data updated successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to update crops market data.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }

    // Delete method to display the confirmation form for deleting a crops market data record
    public function delete($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        return view('staff_crops_market/staff_crops_market_data_delete', $data);
    }

    // Destroy method to handle the deletion of a crops market data record
    public function destroy($id)
    {
        $deleteResult = $this->cropsFarmMarketingDataModel->delete($id);

        if ($deleteResult) {
            session()->setFlashdata('success', 'Crops market data deleted successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to delete crops market data.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }
}
