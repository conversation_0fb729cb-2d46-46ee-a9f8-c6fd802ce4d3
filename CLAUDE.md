# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

Agristats is a multi-portal agricultural data management system built on CodeIgniter 4.6.0. The application serves three distinct user types through separate portal interfaces:

- **Dakoii Portal** (`/dakoii/*`) - System administration and master data management
- **Admin Portal** (`/admin/*`) - Organization-level administration and oversight  
- **Staff Portal** (`/staff/*`) - Field data collection and farm management

## Development Commands

### Testing
```bash
composer test          # Run PHPUnit test suite
composer install       # Install dependencies
composer dump-autoload  # Regenerate autoloader
```

### CodeIgniter CLI (requires PHP in PATH)
```bash
./spark list           # Show available CLI commands
./spark serve          # Start development server
./spark migrate        # Run database migrations
./spark db:seed        # Run database seeders
```

## Architecture & Code Organization

### Multi-Portal Structure
The system uses separate authentication and session management for each portal:

- **Dakoii**: System-wide control, organization management, master data (crops, fertilizers, livestock)
- **Admin**: Organization-specific user management and data oversight
- **Staff**: Field data collection, farmer management, farm operations

### RESTful Implementation
Controllers follow RESTful conventions with proper HTTP verb usage:

```php
// Standard RESTful pattern used throughout
GET    /resource           -> index()   // List resources
GET    /resource/create    -> create()  // Show create form
POST   /resource           -> store()   // Store new resource
GET    /resource/{id}      -> show($id) // Show specific resource
GET    /resource/{id}/edit -> edit($id) // Show edit form
PUT    /resource/{id}      -> update($id) // Update resource
DELETE /resource/{id}      -> destroy($id) // Delete resource
```

### Key Controllers
- **DakoiiAuth/AdminAuth/Home** - Authentication for each portal
- **FarmerController** - Farmer management (RESTful)
- **StaffFarmsController** - Farm and farm block management
- **CropsFarmBlocksController** - Crop data collection
- **Staff_Reports** - Agricultural reporting and analytics

### Database Structure
Geographic hierarchy: `Country → Province → District → LLG → Ward`

Core agricultural entities:
```
farmer_information
├── crops_farm_block
│   ├── crops_farm_crops_data
│   ├── crops_farm_fertilizer_data
│   ├── crops_farm_harvest_data
│   └── crops_farm_marketing_data
└── livestock_farm_block
    └── livestock_farm_data
```

## Development Guidelines

### File Organization
- Controllers: `app/Controllers/` (with subdirectories for Staff/, Admin/, etc.)
- Models: `app/Models/` (follow Entity + Model naming pattern)
- Views: `app/Views/` (organized by controller/feature)
- Routes: `app/Config/Routes.php` (grouped by portal)

### Authentication & Security
- Each portal has independent authentication (session isolation)
- Use filters for route protection: `AdminAuth`, `Auth` filters
- CSRF protection enabled by default
- All database interactions use Query Builder or Model methods

### RESTful Best Practices
When adding new controllers:
1. Implement standard CRUD methods (index, create, store, show, edit, update, destroy)
2. Use proper HTTP verbs in routes
3. Add form compatibility routes for HTML forms using method spoofing
4. Follow the existing naming conventions

### View Patterns
Views follow consistent patterns:
- List pages: `{feature}_index.php`
- Create forms: `{feature}_create.php`  
- Edit forms: `{feature}_edit.php`
- Detail pages: `{feature}_show.php`

### Data Models
Models extend `CodeIgniter\Model` and include:
- Proper table definitions and primary keys
- Validation rules in `$validationRules`
- Relationship methods when needed
- Consistent naming (e.g., `FarmerInformationModel` for `farmer_information` table)

## Key Features & Implementation Notes

### Multi-Organization Support
- Organizations are isolated through database design
- Users belong to specific organizations with role-based permissions
- Geographic data is shared but agricultural data is organization-specific

### Geographic Location Management
Implements full hierarchy with CRUD operations for each level. Import/export functionality available via CSV for bulk operations.

### Agricultural Data Collection
Comprehensive data collection system covering:
- Farmer demographics and contact information
- Farm blocks with GPS coordinates
- Crop planting, fertilizer application, and harvest data
- Livestock management and production records
- Market pricing and sales information

### Reporting System
Dashboard-style reporting with filtering capabilities across all agricultural data types. Reports are role-based and organization-specific.

## Development Environment

**Local Setup**: XAMPP environment
**Base URL**: `http://localhost/agristats/`
**Database**: MySQL with comprehensive agricultural schema
**PHP Version**: 7.4+ required (CodeIgniter 4.6.0 compatibility)

## Common Development Tasks

### Adding New Agricultural Data Types
1. Create model in `app/Models/` following naming conventions
2. Add RESTful controller methods
3. Create corresponding views following established patterns
4. Update routes in appropriate portal section
5. Add to relevant reporting dashboards

### Portal-Specific Development
Each portal has distinct user flows and permissions. When working on features:
- **Dakoii**: Focus on system-wide functionality and master data
- **Admin**: Organization-level oversight and user management  
- **Staff**: Field operations and data collection workflows

### Database Migrations
Use CodeIgniter 4 migration system for schema changes. Existing system has extensive relational structure - always verify foreign key constraints when modifying.

## Testing & Quality Assurance

- PHPUnit test suite configured with code coverage
- Follow existing patterns for controller and model testing
- Comprehensive development guides available in `/dev_guides/` directory
- RESTful compliance analysis and architecture documentation maintained