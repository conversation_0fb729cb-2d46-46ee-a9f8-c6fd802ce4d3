<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxCountryModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\CropsModel;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel;
use App\Models\CropsFarmDiseaseDataModel;

class AdminCropsController extends BaseController
{
    protected $farmerModel;
    protected $provinceModel;
    protected $countryModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $cropsModel;
    protected $cropBuyersModel;
    protected $cropsFarmBlockModel;
    protected $cropsFarmCropsDataModel;
    protected $cropsFarmMarketingDataModel;
    protected $cropsFarmFertilizerDataModel;
    protected $cropsFarmPesticidesDataModel;
    protected $cropsFarmHarvestDataModel;
    protected $cropsFarmDiseaseDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        
        $this->farmerModel = new FarmerInformationModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->countryModel = new AdxCountryModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->cropsModel = new CropsModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->cropsFarmBlockModel = new CropsFarmBlockModel();
        $this->cropsFarmCropsDataModel = new CropsFarmCropsDataModel();
        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
        $this->cropsFarmFertilizerDataModel = new CropsFarmFertilizerDataModel();
        $this->cropsFarmPesticidesDataModel = new CropsFarmPesticidesDataModel();
        $this->cropsFarmHarvestDataModel = new CropsFarmHarvestDataModel();
        $this->cropsFarmDiseaseDataModel = new CropsFarmDiseaseDataModel();
    }

    /**
     * Display crops list with summary statistics
     */
    public function index()
    {
        // Get crops with aggregated data
        $cropsData = $this->getCropsWithStats();
        
        $data = [
            'title' => 'Crops Data Report',
            'page_header' => 'Crops Data Report',
            'page_desc' => 'Overview of all crops with farm blocks and statistics',
            'menu' => 'admin-reports-crops',
            'crops_data' => $cropsData,
            'summary_stats' => $this->getCropsSummaryStats()
        ];

        return view('admin_crops/admin_crops_index', $data);
    }

    /**
     * Display detailed information for a specific crop
     */
    public function show($cropId)
    {
        // Validate crop exists
        $crop = $this->cropsModel->find($cropId);
        if (!$crop) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Crop not found');
        }

        // Get detailed crop data
        $cropDetails = $this->getCropDetailedData($cropId);
        
        $data = [
            'title' => 'Crop Details - ' . $crop['crop_name'],
            'page_header' => 'Crop Details: ' . $crop['crop_name'],
            'page_desc' => 'Comprehensive data for ' . $crop['crop_name'],
            'menu' => 'admin-reports-crops',
            'crop' => $crop,
            'crop_details' => $cropDetails,
            'crop_stats' => $this->getCropStats($cropId),
            'farm_blocks' => $this->getCropFarmBlocks($cropId),
            'diseases_data' => $this->getCropDiseasesData($cropId),
            'fertilizers_data' => $this->getCropFertilizersData($cropId),
            'pesticides_data' => $this->getCropPesticidesData($cropId),
            'harvest_data' => $this->getCropHarvestData($cropId),
            'marketing_data' => $this->getCropMarketingData($cropId),
            'farmers_involved' => $this->getCropFarmersData($cropId)
        ];

        return view('admin_crops/admin_crops_show', $data);
    }

    /**
     * Test page with dummy data for charts
     */
    public function test()
    {
        $data = [
            'title' => 'Chart Test - Dummy Data',
            'page_header' => 'Chart Test with Dummy Data',
            'page_desc' => 'Testing charts with hardcoded dummy data',
            'menu' => 'admin-reports-crops'
        ];

        return view('admin_crops/admin_crops_show_test', $data);
    }

    /**
     * Get crops with aggregated statistics
     */
    private function getCropsWithStats()
    {
        $crops = $this->cropsModel->findAll();
        $cropsData = [];

        foreach ($crops as $crop) {
            $cropId = $crop['id'];
            
            // Get blocks count
            $blocksCount = $this->cropsFarmBlockModel
                ->where('crop_id', $cropId)
                ->where('status !=', 'deleted')
                ->countAllResults();

            // Get total hectares
            $totalHectares = $this->cropsFarmCropsDataModel
                ->selectSum('hectares')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
                ->where('crops_farm_blocks.crop_id', $cropId)
                ->where('crops_farm_crops_data.action_type', 'add')
                ->where('crops_farm_crops_data.status', 'active')
                ->get()
                ->getRow()
                ->hectares ?? 0;

            // Get farmers count
            $farmersCount = $this->cropsFarmBlockModel
                ->select('farmer_id')
                ->where('crop_id', $cropId)
                ->where('status !=', 'deleted')
                ->groupBy('farmer_id')
                ->countAllResults();

            // Get total plants
            $totalPlants = $this->cropsFarmCropsDataModel
                ->selectSum('number_of_plants')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
                ->where('crops_farm_blocks.crop_id', $cropId)
                ->where('crops_farm_crops_data.action_type', 'add')
                ->where('crops_farm_crops_data.status', 'active')
                ->get()
                ->getRow()
                ->number_of_plants ?? 0;

            $cropsData[] = [
                'id' => $crop['id'],
                'crop_name' => $crop['crop_name'],
                'crop_icon' => $crop['crop_icon'],
                'crop_color_code' => $crop['crop_color_code'],
                'blocks_count' => $blocksCount,
                'total_hectares' => $totalHectares,
                'farmers_count' => $farmersCount,
                'total_plants' => $totalPlants
            ];
        }

        return $cropsData;
    }

    /**
     * Get overall summary statistics
     */
    private function getCropsSummaryStats()
    {
        return [
            'total_crops' => $this->cropsModel->countAll(),
            'total_blocks' => $this->cropsFarmBlockModel->where('status !=', 'deleted')->countAllResults(),
            'total_farmers' => $this->cropsFarmBlockModel
                ->select('farmer_id')
                ->where('status !=', 'deleted')
                ->groupBy('farmer_id')
                ->countAllResults(),
            'total_hectares' => $this->cropsFarmCropsDataModel
                ->selectSum('hectares')
                ->where('action_type', 'add')
                ->where('status', 'active')
                ->get()
                ->getRow()
                ->hectares ?? 0
        ];
    }

    /**
     * Get detailed data for a specific crop
     */
    private function getCropDetailedData($cropId)
    {
        return $this->cropsFarmCropsDataModel
            ->select('
                crops_farm_crops_data.*,
                crops_farm_blocks.block_code,
                crops_farm_blocks.block_site,
                farmer_information.given_name,
                farmer_information.surname,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_blocks.crop_id', $cropId)
            ->where('crops_farm_crops_data.status', 'active')
            ->orderBy('crops_farm_crops_data.action_date', 'DESC')
            ->findAll();
    }

    /**
     * Get statistics for a specific crop
     */
    private function getCropStats($cropId)
    {
        $totalHectares = $this->cropsFarmCropsDataModel
            ->selectSum('hectares')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->where('crops_farm_blocks.crop_id', $cropId)
            ->where('crops_farm_crops_data.action_type', 'add')
            ->where('crops_farm_crops_data.status', 'active')
            ->get()
            ->getRow()
            ->hectares ?? 0;

        $totalPlants = $this->cropsFarmCropsDataModel
            ->selectSum('number_of_plants')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->where('crops_farm_blocks.crop_id', $cropId)
            ->where('crops_farm_crops_data.action_type', 'add')
            ->where('crops_farm_crops_data.status', 'active')
            ->get()
            ->getRow()
            ->number_of_plants ?? 0;

        $blocksCount = $this->cropsFarmBlockModel
            ->where('crop_id', $cropId)
            ->where('status !=', 'deleted')
            ->countAllResults();

        $farmersCount = $this->cropsFarmBlockModel
            ->select('farmer_id')
            ->where('crop_id', $cropId)
            ->where('status !=', 'deleted')
            ->groupBy('farmer_id')
            ->countAllResults();

        return [
            'total_hectares' => $totalHectares,
            'total_plants' => $totalPlants,
            'blocks_count' => $blocksCount,
            'farmers_count' => $farmersCount
        ];
    }

    /**
     * Get farm blocks for a specific crop
     */
    private function getCropFarmBlocks($cropId)
    {
        return $this->cropsFarmBlockModel
            ->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                farmer_information.farmer_code,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_blocks.crop_id', $cropId)
            ->where('crops_farm_blocks.status !=', 'deleted')
            ->orderBy('crops_farm_blocks.created_at', 'DESC')
            ->findAll();
    }

    /**
     * Get diseases data for a specific crop
     */
    private function getCropDiseasesData($cropId)
    {
        return $this->cropsFarmDiseaseDataModel
            ->select('
                crops_farm_disease_data.*,
                crops_farm_blocks.block_code,
                farmer_information.given_name,
                farmer_information.surname
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_disease_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->where('crops_farm_disease_data.crop_id', $cropId)
            ->where('crops_farm_disease_data.status', 'active')
            ->orderBy('crops_farm_disease_data.action_date', 'DESC')
            ->findAll();
    }

    /**
     * Get fertilizers data for a specific crop
     */
    private function getCropFertilizersData($cropId)
    {
        return $this->cropsFarmFertilizerDataModel
            ->select('
                crops_farm_fertilizer_data.*,
                crops_farm_blocks.block_code,
                farmer_information.given_name,
                farmer_information.surname
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->where('crops_farm_fertilizer_data.crop_id', $cropId)
            ->where('crops_farm_fertilizer_data.status', 'active')
            ->orderBy('crops_farm_fertilizer_data.action_date', 'DESC')
            ->findAll();
    }

    /**
     * Get pesticides data for a specific crop
     */
    private function getCropPesticidesData($cropId)
    {
        return $this->cropsFarmPesticidesDataModel
            ->select('
                crops_farm_pesticides_data.*,
                crops_farm_blocks.block_code,
                farmer_information.given_name,
                farmer_information.surname
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->where('crops_farm_pesticides_data.crop_id', $cropId)
            ->where('crops_farm_pesticides_data.status', 'active')
            ->orderBy('crops_farm_pesticides_data.action_date', 'DESC')
            ->findAll();
    }

    /**
     * Get harvest data for a specific crop
     */
    private function getCropHarvestData($cropId)
    {
        return $this->cropsFarmHarvestDataModel
            ->select('
                crops_farm_harvest_data.*,
                crops_farm_blocks.block_code,
                farmer_information.given_name,
                farmer_information.surname
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_harvest_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->where('crops_farm_harvest_data.crop_id', $cropId)
            ->where('crops_farm_harvest_data.status', 'active')
            ->orderBy('crops_farm_harvest_data.harvest_date', 'DESC')
            ->findAll();
    }

    /**
     * Get marketing data for a specific crop
     */
    private function getCropMarketingData($cropId)
    {
        return $this->cropsFarmMarketingDataModel
            ->select('
                crops_farm_marketing_data.*,
                crops_farm_blocks.block_code,
                farmer_information.given_name,
                farmer_information.surname,
                crop_buyers.name as buyer_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_marketing_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
            ->where('crops_farm_marketing_data.crop_id', $cropId)
            ->where('crops_farm_marketing_data.status', 'active')
            ->orderBy('crops_farm_marketing_data.market_date', 'DESC')
            ->findAll();
    }

    /**
     * Get farmers involved in a specific crop
     */
    private function getCropFarmersData($cropId)
    {
        return $this->farmerModel
            ->select('
                farmer_information.*,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name,
                COUNT(crops_farm_blocks.id) as blocks_count
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.farmer_id = farmer_information.id')
            ->join('adx_district', 'adx_district.id = farmer_information.district_id')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id')
            ->where('crops_farm_blocks.crop_id', $cropId)
            ->where('crops_farm_blocks.status !=', 'deleted')
            ->where('farmer_information.status', 'active')
            ->groupBy('farmer_information.id')
            ->orderBy('farmer_information.given_name', 'ASC')
            ->findAll();
    }
}
