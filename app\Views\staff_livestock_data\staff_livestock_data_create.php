<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>">Livestock Blocks</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data') ?>">Farm Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Add New</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Block Information -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Farm Block Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Block Code:</strong><br>
                            <span class="badge bg-primary fs-6"><?= esc($block['block_code']) ?></span>
                        </div>
                        <div class="col-md-4">
                            <strong>Farmer:</strong><br>
                            <?= esc($block['given_name'] . ' ' . $block['surname']) ?>
                        </div>
                        <div class="col-md-4">
                            <strong>Location:</strong><br>
                            <?= esc($block['block_site']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>Add New Livestock Farm Data
                    </h5>
                    <p class="card-text mb-0 text-muted">Enter livestock farm data information for this block</p>
                </div>
                <div class="card-body">
                    <!-- Error Messages -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>Please correct the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Form -->
                    <form action="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data') ?>" method="POST">
                        <?= csrf_field() ?>

                        <div class="row">
                            <!-- Livestock Type -->
                            <div class="col-md-4 mb-3">
                                <label for="livestock_id" class="form-label">Livestock Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="livestock_id" name="livestock_id" required>
                                    <option value="">Select Livestock Type</option>
                                    <?php foreach ($livestock_types as $livestock): ?>
                                        <option value="<?= $livestock['id'] ?>" <?= old('livestock_id') == $livestock['id'] ? 'selected' : '' ?>>
                                            <?= esc($livestock['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Breed -->
                            <div class="col-md-4 mb-3">
                                <label for="breed" class="form-label">Breed <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="breed" name="breed"
                                       value="<?= old('breed') ?>" required maxlength="255">
                            </div>

                            <!-- Action Date -->
                            <div class="col-md-4 mb-3">
                                <label for="action_date" class="form-label">Action Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="action_date" name="action_date"
                                       value="<?= old('action_date') ?>" required>
                            </div>

                            <!-- Male Total -->
                            <div class="col-md-6 mb-3">
                                <label for="he_total" class="form-label">Male Total</label>
                                <input type="number" class="form-control" id="he_total" name="he_total" 
                                       value="<?= old('he_total') ?>" min="0" step="1">
                            </div>

                            <!-- Female Total -->
                            <div class="col-md-6 mb-3">
                                <label for="she_total" class="form-label">Female Total</label>
                                <input type="number" class="form-control" id="she_total" name="she_total" 
                                       value="<?= old('she_total') ?>" min="0" step="1">
                            </div>

                            <!-- Pasture Type -->
                            <div class="col-md-6 mb-3">
                                <label for="pasture_type" class="form-label">Pasture Type</label>
                                <input type="text" class="form-control" id="pasture_type" name="pasture_type" 
                                       value="<?= old('pasture_type') ?>" maxlength="100">
                            </div>

                            <!-- Growth Stage -->
                            <div class="col-md-6 mb-3">
                                <label for="growth_stage" class="form-label">Growth Stage</label>
                                <input type="text" class="form-control" id="growth_stage" name="growth_stage" 
                                       value="<?= old('growth_stage') ?>" maxlength="100">
                            </div>

                            <!-- Cost per Livestock -->
                            <div class="col-md-4 mb-3">
                                <label for="cost_per_livestock" class="form-label">Cost per Livestock (K)</label>
                                <input type="number" class="form-control" id="cost_per_livestock" name="cost_per_livestock" 
                                       value="<?= old('cost_per_livestock') ?>" min="0" step="0.01">
                            </div>

                            <!-- Low Price per Livestock -->
                            <div class="col-md-4 mb-3">
                                <label for="low_price_per_livestock" class="form-label">Lowest Price (K)</label>
                                <input type="number" class="form-control" id="low_price_per_livestock" name="low_price_per_livestock" 
                                       value="<?= old('low_price_per_livestock') ?>" min="0" step="0.01">
                            </div>

                            <!-- High Price per Livestock -->
                            <div class="col-md-4 mb-3">
                                <label for="high_price_per_livestock" class="form-label">Highest Price (K)</label>
                                <input type="number" class="form-control" id="high_price_per_livestock" name="high_price_per_livestock" 
                                       value="<?= old('high_price_per_livestock') ?>" min="0" step="0.01">
                            </div>

                            <!-- Comments -->
                            <div class="col-12 mb-3">
                                <label for="comments" class="form-label">Comments</label>
                                <textarea class="form-control" id="comments" name="comments" rows="3"><?= old('comments') ?></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2 for better dropdowns
    $('#livestock_id').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Validate price range
    $('#low_price_per_livestock, #high_price_per_livestock').on('input', function() {
        const lowPrice = parseFloat($('#low_price_per_livestock').val()) || 0;
        const highPrice = parseFloat($('#high_price_per_livestock').val()) || 0;
        
        if (lowPrice > 0 && highPrice > 0 && lowPrice > highPrice) {
            $('#high_price_per_livestock')[0].setCustomValidity('Highest price must be greater than or equal to lowest price');
        } else {
            $('#high_price_per_livestock')[0].setCustomValidity('');
        }
    });
});
</script>

<?= $this->endSection() ?>
