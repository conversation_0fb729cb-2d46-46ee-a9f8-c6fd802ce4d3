<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    PesticidesModel,
    CropsFarmPesticidesDataModel,
    CropsFarmBlockModel
};

class StaffPesticidesDataController extends BaseController
{
    protected $pesticidesModel;
    protected $pesticidesDataModel;
    protected $farmBlockModel;

    public function __construct()
    {
        $this->pesticidesModel = new PesticidesModel();
        $this->pesticidesDataModel = new CropsFarmPesticidesDataModel();
        $this->farmBlockModel = new CropsFarmBlockModel();
        
        // Initialize helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display a listing of pesticides data for all farm blocks
     */
    public function index()
    {
        $district_id = session()->get('district_id');
        $pesticides_data = $this->pesticidesDataModel->getPesticidesReportData();

        $data = [
            'title' => 'Pesticides Data Management',
            'page_header' => 'Pesticides Data Management',
            'pesticides_data' => $pesticides_data
        ];

        return view('staff_pesticides_data/staff_pesticides_data_index', $data);
    }

    /**
     * Show the form for creating new pesticides data
     */
    public function create()
    {
        $district_id = session()->get('district_id');

        // Get farm blocks for the district
        $farm_blocks = $this->farmBlockModel->getFarmBlocksWithDetails($district_id);
        
        // Get all pesticides
        $pesticides = $this->pesticidesModel->where('status', 'active')->findAll();

        $data = [
            'title' => 'Add Pesticides Data',
            'page_header' => 'Add New Pesticides Data',
            'farm_blocks' => $farm_blocks,
            'pesticides' => $pesticides
        ];

        return view('staff_pesticides_data/staff_pesticides_data_create', $data);
    }

    /**
     * Store newly created pesticides data
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'block_id' => 'required|numeric',
            'pesticide_id' => 'permit_empty|numeric',
            'name' => 'required|max_length[100]',
            'brand' => 'permit_empty|max_length[100]',
            'unit_of_measure' => 'required|max_length[50]',
            'unit' => 'required|max_length[50]',
            'quantity' => 'required|decimal',
            'action_date' => 'required|valid_date',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->validator->getErrors());
        }

        // Get block details for crop_id
        $block = $this->farmBlockModel->find($this->request->getPost('block_id'));
        if (!$block) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Invalid farm block selected.');
        }

        $data = [
            'block_id' => $this->request->getPost('block_id'),
            'crop_id' => $block['crop_id'],
            'pesticide_id' => $this->request->getPost('pesticide_id') ?: null,
            'name' => $this->request->getPost('name'),
            'brand' => $this->request->getPost('brand'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active',
            'created_by' => session()->get('user_id')
        ];

        if ($this->pesticidesDataModel->insert($data)) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('success', 'Pesticides data added successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Failed to add pesticides data.');
        }
    }

    /**
     * Display specific pesticides data
     */
    public function show($id)
    {
        $district_id = session()->get('district_id');
        
        $pesticide_data = $this->pesticidesDataModel
            ->select('
                crops_farm_pesticides_data.*,
                crops_farm_blocks.block_code,
                crops_farm_blocks.block_site,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_crops.crop_color_code,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name,
                adx_pesticides.name as pesticide_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
            ->where('crops_farm_pesticides_data.id', $id)
            ->where('crops_farm_blocks.district_id', $district_id)
            ->first();

        if (!$pesticide_data) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('error', 'Pesticides data not found.');
        }

        $data = [
            'title' => 'Pesticides Data Details',
            'page_header' => 'Pesticides Data Details',
            'pesticide_data' => $pesticide_data
        ];

        return view('staff_pesticides_data/staff_pesticides_data_view', $data);
    }

    /**
     * Show the form for editing pesticides data
     */
    public function edit($id)
    {
        $district_id = session()->get('district_id');
        
        $pesticide_data = $this->pesticidesDataModel
            ->select('crops_farm_pesticides_data.*, crops_farm_blocks.district_id')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->where('crops_farm_pesticides_data.id', $id)
            ->where('crops_farm_blocks.district_id', $district_id)
            ->first();

        if (!$pesticide_data) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('error', 'Pesticides data not found.');
        }

        // Get farm blocks for the district
        $farm_blocks = $this->farmBlockModel->getFarmBlocksWithDetails($district_id);
        
        // Get all pesticides
        $pesticides = $this->pesticidesModel->where('status', 'active')->findAll();

        $data = [
            'title' => 'Edit Pesticides Data',
            'page_header' => 'Edit Pesticides Data',
            'pesticide_data' => $pesticide_data,
            'farm_blocks' => $farm_blocks,
            'pesticides' => $pesticides
        ];

        return view('staff_pesticides_data/staff_pesticides_data_edit', $data);
    }

    /**
     * Update pesticides data
     */
    public function update($id)
    {
        $district_id = session()->get('district_id');
        
        // Verify access
        $existing = $this->pesticidesDataModel
            ->select('crops_farm_pesticides_data.id')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->where('crops_farm_pesticides_data.id', $id)
            ->where('crops_farm_blocks.district_id', $district_id)
            ->first();

        if (!$existing) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('error', 'Pesticides data not found.');
        }

        $validation = \Config\Services::validation();
        
        $rules = [
            'block_id' => 'required|numeric',
            'pesticide_id' => 'permit_empty|numeric',
            'name' => 'required|max_length[100]',
            'brand' => 'permit_empty|max_length[100]',
            'unit_of_measure' => 'required|max_length[50]',
            'unit' => 'required|max_length[50]',
            'quantity' => 'required|decimal',
            'action_date' => 'required|valid_date',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                           ->withInput()
                           ->with('errors', $this->validator->getErrors());
        }

        // Get block details for crop_id
        $block = $this->farmBlockModel->find($this->request->getPost('block_id'));
        if (!$block) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Invalid farm block selected.');
        }

        $data = [
            'block_id' => $this->request->getPost('block_id'),
            'crop_id' => $block['crop_id'],
            'pesticide_id' => $this->request->getPost('pesticide_id') ?: null,
            'name' => $this->request->getPost('name'),
            'brand' => $this->request->getPost('brand'),
            'unit_of_measure' => $this->request->getPost('unit_of_measure'),
            'unit' => $this->request->getPost('unit'),
            'quantity' => $this->request->getPost('quantity'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->pesticidesDataModel->update($id, $data)) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('success', 'Pesticides data updated successfully.');
        } else {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Failed to update pesticides data.');
        }
    }

    /**
     * Delete pesticides data
     */
    public function destroy($id)
    {
        $district_id = session()->get('district_id');
        
        // Verify access
        $existing = $this->pesticidesDataModel
            ->select('crops_farm_pesticides_data.id')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->where('crops_farm_pesticides_data.id', $id)
            ->where('crops_farm_blocks.district_id', $district_id)
            ->first();

        if (!$existing) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('error', 'Pesticides data not found.');
        }

        $data = [
            'status' => 'deleted',
            'deleted_by' => session()->get('user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        if ($this->pesticidesDataModel->update($id, $data)) {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('success', 'Pesticides data deleted successfully.');
        } else {
            return redirect()->to('/staff/farms/pesticides_data')
                           ->with('error', 'Failed to delete pesticides data.');
        }
    }
} 