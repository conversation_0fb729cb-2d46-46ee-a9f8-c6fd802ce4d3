<?php

namespace App\Models;

use CodeIgniter\Model;

class FieldVisitsModel extends Model
{
    protected $table            = 'field_visits';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    protected $allowedFields = [
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'locations', //json formatted data
        'gps', //json formatted data
        'officers', //json formatted data
        'date_start',
        'date_end',
        'purpose',
        'achievements', //json formatted data
        'beneficiaries', //json formatted data
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];


    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

   
    // Validation
    protected $validationRules = [
        'country_id'    => 'required|numeric',
        'province_id'   => 'required|numeric',
        'district_id'   => 'required|numeric',
        'llg_id'        => 'required|numeric',
        'date_start'    => 'required',
        'date_end'      => 'required',
        'purpose'       => 'required'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
}
