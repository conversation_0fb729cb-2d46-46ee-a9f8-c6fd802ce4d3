<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Crops Market Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Crops Market Data</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/marketing_data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Item
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Market Data List</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped table-hover" id="marketingDataTable">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Crop</th>
                                <th>Market Date</th>
                                <th>Selling Location</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($marketingData as $data): ?>
                                <tr>
                                    <td><?= esc($data['id']) ?></td>
                                    <td><?= esc($data['crop_name']) ?></td>
                                    <td><?= esc($data['market_date']) ?></td>
                                    <td><?= esc($data['selling_location']) ?></td>
                                    <td>
                                        <span class="badge <?= $data['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= esc(ucfirst($data['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?= base_url('staff/farms/marketing_data/' . esc($data['id'])) ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye me-2"></i>View
                                            </a>
                                            <a href="<?= base_url('staff/farms/marketing_data/' . esc($data['id']) . '/edit') ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </a>
                                            <a href="<?= base_url('staff/farms/marketing_data/' . esc($data['id']) . '/delete') ?>" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#marketingDataTable').DataTable({
        responsive: true,
        order: [[0, "desc"]],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: 5 }
        ],
        language: {
            search: "Search Market Data:",
            lengthMenu: "Show _MENU_ Market Data per page",
            info: "Showing _START_ to _END_ of _TOTAL_ Market Data",
            infoEmpty: "No Market Data found",
            infoFiltered: "(filtered from _MAX_ total Market Data)"
        }
    });
});
</script>
<?= $this->endSection() ?>
