<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0">Training Details</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/trainings') ?>">Trainings</a></li>
                            <li class="breadcrumb-item active" aria-current="page">View</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                    <a href="<?= base_url('staff/extension/trainings/' . $training['id'] . '/edit') ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Training
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i><?= esc($training['topic']) ?>
                    </h5>
                    <p class="card-text mb-0 text-muted">Training ID: <?= $training['id'] ?></p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            
                            <div class="mb-3">
                                <strong>Topic:</strong>
                                <p class="mb-1"><?= esc($training['topic']) ?></p>
                            </div>

                            <div class="mb-3">
                                <strong>Location:</strong>
                                <p class="mb-1"><?= esc($training['locations']) ?></p>
                                <small class="text-muted">LLG: <?= esc($training['llg_name'] ?? 'N/A') ?></small>
                            </div>

                            <div class="mb-3">
                                <strong>GPS Coordinates:</strong>
                                <p class="mb-1"><?= esc($training['gps']) ?></p>
                            </div>

                            <div class="mb-3">
                                <strong>Status:</strong>
                                <p class="mb-1">
                                    <?php
                                    $statusBadges = [
                                        1 => '<span class="badge bg-success">Active</span>',
                                        2 => '<span class="badge bg-info">Completed</span>',
                                        3 => '<span class="badge bg-warning">Pending</span>',
                                        0 => '<span class="badge bg-secondary">Inactive</span>'
                                    ];
                                    echo $statusBadges[$training['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                    ?>
                                </p>
                            </div>
                        </div>

                        <!-- Date Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-calendar me-2"></i>Schedule Information</h6>
                            
                            <div class="mb-3">
                                <strong>Start Date:</strong>
                                <p class="mb-1"><?= date('d F Y', strtotime($training['date_start'])) ?></p>
                            </div>

                            <div class="mb-3">
                                <strong>End Date:</strong>
                                <p class="mb-1"><?= date('d F Y', strtotime($training['date_end'])) ?></p>
                            </div>

                            <div class="mb-3">
                                <strong>Duration:</strong>
                                <p class="mb-1">
                                    <?php
                                    $start = new DateTime($training['date_start']);
                                    $end = new DateTime($training['date_end']);
                                    $duration = $start->diff($end)->days + 1;
                                    echo $duration . ' day' . ($duration > 1 ? 's' : '');
                                    ?>
                                </p>
                            </div>

                            <div class="mb-3">
                                <strong>Created:</strong>
                                <p class="mb-1"><?= date('d F Y, H:i', strtotime($training['created_at'])) ?></p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Training Content -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3"><i class="fas fa-book me-2"></i>Training Content</h6>
                            
                            <div class="mb-4">
                                <strong>Objectives:</strong>
                                <div class="border rounded p-3 bg-light">
                                    <?= nl2br(esc($training['objectives'])) ?>
                                </div>
                            </div>

                            <?php if (!empty($training['content'])): ?>
                                <div class="mb-4">
                                    <strong>Content:</strong>
                                    <div class="border rounded p-3 bg-light">
                                        <?= nl2br(esc($training['content'])) ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($training['materials'])): ?>
                                <div class="mb-4">
                                    <strong>Materials:</strong>
                                    <div class="border rounded p-3 bg-light">
                                        <?= nl2br(esc($training['materials'])) ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <!-- Participants -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-users me-2"></i>Trainers</h6>
                            <?php if (!empty($training['trainers'])): ?>
                                <?php
                                $trainers = json_decode($training['trainers'], true);
                                if (is_array($trainers) && !empty($trainers)):
                                ?>
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($trainers as $trainer): ?>
                                            <li class="list-group-item px-0">
                                                <i class="fas fa-user me-2 text-muted"></i><?= esc($trainer) ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <p class="text-muted">No trainers listed</p>
                                <?php endif; ?>
                            <?php else: ?>
                                <p class="text-muted">No trainers listed</p>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-user-friends me-2"></i>Attendees</h6>
                            <?php if (!empty($training['attendees'])): ?>
                                <?php
                                $attendees = json_decode($training['attendees'], true);
                                if (is_array($attendees) && !empty($attendees)):
                                ?>
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($attendees as $attendee): ?>
                                            <li class="list-group-item px-0">
                                                <i class="fas fa-user me-2 text-muted"></i><?= esc($attendee) ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <p class="text-muted">No attendees listed</p>
                                <?php endif; ?>
                            <?php else: ?>
                                <p class="text-muted">No attendees listed</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-2"></i>Back to List
                                </a>
                                <a href="<?= base_url('staff/extension/trainings/' . $training['id'] . '/edit') ?>" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Edit Training
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
