<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/livestock-farm-blocks') ?>">Livestock Blocks</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Farm Data</li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/livestock-farm-blocks') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Blocks
                    </a>
                    <a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Block Information -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Farm Block Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Block Code:</strong><br>
                            <span class="badge bg-primary fs-6"><?= esc($block['block_code']) ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Farmer:</strong><br>
                            <?= esc($block['given_name'] . ' ' . $block['surname']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Location:</strong><br>
                            <?= esc($block['village']) ?> - <?= esc($block['block_site']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>District:</strong><br>
                            <?= esc($block['district_name']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>Livestock Farm Data
                    </h5>
                    <p class="card-text mb-0 text-muted">Manage livestock farm data records for this block</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="livestockDataTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Block Code</th>
                                    <th>Farmer</th>
                                    <th>Livestock Type</th>
                                    <th>Breed</th>
                                    <th>Male/Female</th>
                                    <th>Pasture Type</th>
                                    <th>Growth Stage</th>
                                    <th>Cost/Head</th>
                                    <th>Price Range</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($livestock_data)): ?>
                                    <?php foreach ($livestock_data as $data): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($data['block_code']) ?></strong><br>
                                                <small class="text-muted"><?= esc($data['block_site']) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($data['given_name'] . ' ' . $data['surname']) ?></strong><br>
                                                <small class="text-muted">ID: <?= esc($data['farmer_code']) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?= esc($data['livestock_name']) ?></span>
                                            </td>
                                            <td><?= esc($data['breed']) ?></td>
                                            <td>
                                                <span class="badge bg-info">M: <?= esc($data['he_total']) ?></span>
                                                <span class="badge bg-warning">F: <?= esc($data['she_total']) ?></span>
                                            </td>
                                            <td><?= esc($data['pasture_type']) ?></td>
                                            <td><?= esc($data['growth_stage']) ?></td>
                                            <td>K<?= number_format($data['cost_per_livestock'], 2) ?></td>
                                            <td>
                                                <?php if ($data['low_price_per_livestock'] > 0 || $data['high_price_per_livestock'] > 0): ?>
                                                    K<?= number_format($data['low_price_per_livestock'], 2) ?> - 
                                                    K<?= number_format($data['high_price_per_livestock'], 2) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Not set</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($data['action_date'])): ?>
                                                    <?= date('d-m-Y', strtotime($data['action_date'])) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Not set</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                // Check if data is more than 1 day old
                                                $createdAt = new DateTime($data['created_at']);
                                                $now = new DateTime();
                                                $diff = $now->diff($createdAt);
                                                $canDelete = $diff->days < 1;
                                                ?>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data/' . $data['id']) ?>"
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/livestock/blocks/' . $block['id'] . '/data/' . $data['id'] . '/edit') ?>"
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($canDelete): ?>
                                                        <button type="button" class="btn btn-sm btn-danger"
                                                                onclick="confirmDelete(<?= $block['id'] ?>, <?= $data['id'] ?>)"
                                                                title="Delete (within 24 hours)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-sm btn-secondary"
                                                                disabled
                                                                title="Cannot delete - data is more than 1 day old (created: <?= date('d-m-Y H:i', strtotime($data['created_at'])) ?>)">
                                                            <i class="fas fa-lock"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this livestock farm data record?</p>
                <p class="text-warning"><strong>Note:</strong> You can only delete records that are less than 1 day old.</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Check if table has data
    const tableHasData = $('#livestockDataTable tbody tr').length > 0 &&
                        !$('#livestockDataTable tbody tr').first().find('td[colspan]').length;

    if (tableHasData) {
        // Initialize DataTable only if there's data
        $('#livestockDataTable').DataTable({
            responsive: true,
            order: [[0, "desc"]],
            pageLength: 25,
            columnDefs: [
                { orderable: false, targets: -1 }
            ],
            language: {
                search: "Search livestock data:",
                lengthMenu: "Show _MENU_ records per page",
                info: "Showing _START_ to _END_ of _TOTAL_ records",
                infoEmpty: "No livestock data found",
                infoFiltered: "(filtered from _MAX_ total records)"
            }
        });
    } else {
        // Add empty state message if no data
        if ($('#livestockDataTable tbody tr').length === 0) {
            $('#livestockDataTable tbody').html(
                '<tr><td colspan="11" class="text-center text-muted py-4">' +
                '<i class="fas fa-info-circle me-2"></i>No livestock farm data found<br>' +
                '<small>Click "Add New Data" to create your first record</small>' +
                '</td></tr>'
            );
        }

        // Apply basic styling without DataTables
        $('#livestockDataTable').addClass('table-hover');
    }
});

function confirmDelete(blockId, id) {
    document.getElementById('deleteForm').action = '<?= base_url('staff/livestock/blocks/') ?>' + blockId + '/data/' + id + '/delete';
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
