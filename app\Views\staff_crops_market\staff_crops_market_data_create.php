<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Create Crops Market Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms/marketing_data') ?>">Crops Market Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Create Item</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Create Market Data</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->has('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul>
                                <?php foreach (session('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('staff/farms/marketing_data') ?>" method="post" class="needs-validation" novalidate>
                        <?= csrf_field() ?>

                        <!-- Location Information (Auto-filled) -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Location Information</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Country:</strong> Papua New Guinea
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Province:</strong> <?= esc($province_name) ?>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>District:</strong> <?= esc($district_name) ?>
                                        </div>
                                    </div>
                                    <small class="text-muted">These location details will be automatically saved with your marketing data.</small>
                                </div>
                            </div>
                        </div>

                        <!-- Farmer and Buyer Selection -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="farmer_id" class="form-label">Farmer <span class="text-danger">*</span></label>
                                <select class="form-select select2-farmer" id="farmer_id" name="farmer_id" required>
                                    <option value="">Search and select farmer...</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= esc($farmer['id']) ?>" <?= old('farmer_id') == $farmer['id'] ? 'selected' : '' ?>>
                                            <?= esc($farmer['farmer_code']) ?> - <?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a farmer.
                                </div>
                                <small class="form-text text-muted">Type to search by farmer code or name</small>
                            </div>

                            <!-- Buyer Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="buyer_id" class="form-label">Buyer <span class="text-danger">*</span></label>
                                <select class="form-select select2-buyer" id="buyer_id" name="buyer_id" required>
                                    <option value="">Search and select buyer...</option>
                                    <?php foreach ($buyers as $buyer): ?>
                                        <option value="<?= esc($buyer['id']) ?>" <?= old('buyer_id') == $buyer['id'] ? 'selected' : '' ?>>
                                            <?= esc($buyer['buyer_code'] ?? '') ?> - <?= esc($buyer['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a buyer.
                                </div>
                                <small class="form-text text-muted">Type to search by buyer code or name</small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Crop Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                                <select class="form-select" id="crop_id" name="crop_id" required>
                                    <option value="">Select Crop</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= esc($crop['id']) ?>" <?= old('crop_id') == $crop['id'] ? 'selected' : '' ?>>
                                            <?= esc($crop['crop_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a crop.
                                </div>
                            </div>
                            <!-- Market Date -->
                            <div class="col-md-6 mb-3">
                                <label for="market_date" class="form-label">Market Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="market_date" name="market_date"
                                       value="<?= old('market_date', date('Y-m-d')) ?>" required>
                                <div class="invalid-feedback">
                                    Please provide a valid market date.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- LLG Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="llg_id" class="form-label">Local Level Government (LLG)</label>
                                <select class="form-select" id="llg_id" name="llg_id">
                                    <option value="">Select LLG (Optional)</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= esc($llg['id']) ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                            <?= esc($llg['llgcode'] ?? '') ?> - <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Optional - Select the LLG where the marketing activity took place</small>
                            </div>

                            <!-- Market Stage -->
                            <div class="col-md-6 mb-3">
                                <label for="market_stage" class="form-label">Market Stage <span class="text-danger">*</span></label>
                                <select class="form-select" id="market_stage" name="market_stage" required>
                                    <option value="">Select Market Stage</option>
                                    <option value="Farm Gate" <?= old('market_stage') == 'Farm Gate' ? 'selected' : '' ?>>Farm Gate</option>
                                    <option value="Local Market" <?= old('market_stage') == 'Local Market' ? 'selected' : '' ?>>Local Market</option>
                                    <option value="Regional Market" <?= old('market_stage') == 'Regional Market' ? 'selected' : '' ?>>Regional Market</option>
                                    <option value="Export Market" <?= old('market_stage') == 'Export Market' ? 'selected' : '' ?>>Export Market</option>
                                    <option value="Processing Plant" <?= old('market_stage') == 'Processing Plant' ? 'selected' : '' ?>>Processing Plant</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a market stage.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Selling Location -->
                            <div class="col-md-12 mb-3">
                                <label for="selling_location" class="form-label">Selling Location <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="selling_location" name="selling_location"
                                       value="<?= old('selling_location') ?>" placeholder="Enter selling location" required>
                                <div class="invalid-feedback">
                                    Please provide the selling location.
                                </div>
                            </div>
                        </div>
                        <!-- Product Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5><i class="fas fa-box me-2"></i>Product Information</h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Product -->
                            <div class="col-md-6 mb-3">
                                <label for="product" class="form-label">Product <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="product" name="product"
                                       value="<?= old('product') ?>" placeholder="Enter product name" required>
                                <div class="invalid-feedback">
                                    Please provide the product name.
                                </div>
                            </div>

                            <!-- Product Type -->
                            <div class="col-md-6 mb-3">
                                <label for="product_type" class="form-label">Product Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="product_type" name="product_type" required>
                                    <option value="">Select Product Type</option>
                                    <option value="Fresh" <?= old('product_type') == 'Fresh' ? 'selected' : '' ?>>Fresh</option>
                                    <option value="Dried" <?= old('product_type') == 'Dried' ? 'selected' : '' ?>>Dried</option>
                                    <option value="Processed" <?= old('product_type') == 'Processed' ? 'selected' : '' ?>>Processed</option>
                                    <option value="Semi-Processed" <?= old('product_type') == 'Semi-Processed' ? 'selected' : '' ?>>Semi-Processed</option>
                                    <option value="Raw" <?= old('product_type') == 'Raw' ? 'selected' : '' ?>>Raw</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a product type.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Description -->
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="Enter product description"><?= old('description') ?></textarea>
                            </div>
                        </div>

                        <!-- Quantity and Pricing -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5><i class="fas fa-calculator me-2"></i>Quantity and Pricing</h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Unit -->
                            <div class="col-md-4 mb-3">
                                <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="unit" name="unit"
                                       value="<?= old('unit') ?>" placeholder="Enter unit value" step="0.01" min="0.01" required>
                                <div class="invalid-feedback">
                                    Please provide a valid unit value.
                                </div>
                            </div>

                            <!-- Unit of Measure -->
                            <div class="col-md-4 mb-3">
                                <label for="unit_of_measure" class="form-label">Unit of Measure <span class="text-danger">*</span></label>
                                <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                    <option value="">Select Unit</option>
                                    <option value="kg" <?= old('unit_of_measure') == 'kg' ? 'selected' : '' ?>>Kilograms (kg)</option>
                                    <option value="g" <?= old('unit_of_measure') == 'g' ? 'selected' : '' ?>>Grams (g)</option>
                                    <option value="lbs" <?= old('unit_of_measure') == 'lbs' ? 'selected' : '' ?>>Pounds (lbs)</option>
                                    <option value="bags" <?= old('unit_of_measure') == 'bags' ? 'selected' : '' ?>>Bags</option>
                                    <option value="boxes" <?= old('unit_of_measure') == 'boxes' ? 'selected' : '' ?>>Boxes</option>
                                    <option value="bunches" <?= old('unit_of_measure') == 'bunches' ? 'selected' : '' ?>>Bunches</option>
                                    <option value="pieces" <?= old('unit_of_measure') == 'pieces' ? 'selected' : '' ?>>Pieces</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a unit of measure.
                                </div>
                            </div>

                            <!-- Quantity -->
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity"
                                       value="<?= old('quantity') ?>" placeholder="Enter quantity" step="0.01" min="0.01" required>
                                <div class="invalid-feedback">
                                    Please provide a valid quantity.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Market Price per Unit -->
                            <div class="col-md-6 mb-3">
                                <label for="market_price_per_unit" class="form-label">Market Price per Unit (PGK) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="market_price_per_unit" name="market_price_per_unit"
                                       value="<?= old('market_price_per_unit') ?>" placeholder="Enter price per unit" step="0.01" min="0.01" required>
                                <div class="invalid-feedback">
                                    Please provide a valid market price per unit.
                                </div>
                            </div>

                            <!-- Total Freight Cost -->
                            <div class="col-md-6 mb-3">
                                <label for="total_freight_cost" class="form-label">Total Freight Cost (PGK)</label>
                                <input type="number" class="form-control" id="total_freight_cost" name="total_freight_cost"
                                       value="<?= old('total_freight_cost') ?>" placeholder="Enter freight cost" step="0.01" min="0">
                                <small class="form-text text-muted">Optional - Leave blank if no freight cost</small>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                          placeholder="Enter any additional notes or remarks"><?= old('remarks') ?></textarea>
                            </div>
                        </div>
                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('staff/farms/marketing_data') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Marketing Data
                                    </button>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize Select2 for farmer dropdown with search
    $('.select2-farmer').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search and select farmer...',
        allowClear: true,
        width: '100%',
        matcher: function(params, data) {
            // If there are no search terms, return all of the data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Do not display the item if there is no 'text' property
            if (typeof data.text === 'undefined') {
                return null;
            }

            // `params.term` is the user's search term
            var term = params.term.toLowerCase();
            var text = data.text.toLowerCase();

            // Check if the text contains the term
            if (text.indexOf(term) > -1) {
                return data;
            }

            // Return `null` if the term should not be displayed
            return null;
        }
    });

    // Initialize Select2 for buyer dropdown with search
    $('.select2-buyer').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search and select buyer...',
        allowClear: true,
        width: '100%',
        matcher: function(params, data) {
            // If there are no search terms, return all of the data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Do not display the item if there is no 'text' property
            if (typeof data.text === 'undefined') {
                return null;
            }

            // `params.term` is the user's search term
            var term = params.term.toLowerCase();
            var text = data.text.toLowerCase();

            // Check if the text contains the term
            if (text.indexOf(term) > -1) {
                return data;
            }

            // Return `null` if the term should not be displayed
            return null;
        }
    });

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Calculate total value when price or quantity changes
    $('#market_price_per_unit, #quantity').on('input', function() {
        var price = parseFloat($('#market_price_per_unit').val()) || 0;
        var quantity = parseFloat($('#quantity').val()) || 0;
        var totalValue = price * quantity;

        if (totalValue > 0) {
            $('#total_value_display').remove();
            $('<div id="total_value_display" class="alert alert-info mt-2">' +
              '<strong>Total Value: PGK ' + totalValue.toFixed(2) + '</strong></div>')
              .insertAfter('#market_price_per_unit').parent();
        } else {
            $('#total_value_display').remove();
        }
    });

    // Handle Select2 validation styling
    $('.select2-farmer, .select2-buyer').on('select2:close', function() {
        var element = $(this);
        if (element.val() && element.val().length > 0) {
            element.removeClass('is-invalid').addClass('is-valid');
        } else if (element.prop('required')) {
            element.removeClass('is-valid').addClass('is-invalid');
        }
    });

    // Custom validation for Select2 elements
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check Select2 required fields
        $('.select2-farmer, .select2-buyer').each(function() {
            var element = $(this);
            if (element.prop('required') && (!element.val() || element.val().length === 0)) {
                element.addClass('is-invalid');
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
});
</script>
<?= $this->endSection() ?>
