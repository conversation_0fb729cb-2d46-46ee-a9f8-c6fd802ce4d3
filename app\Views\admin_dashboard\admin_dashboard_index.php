<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active">Admin Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Users</span>
                        <span class="info-box-number"><?= number_format($stats['total_users']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: <?= $stats['total_users'] > 0 ? ($stats['active_users'] / $stats['total_users']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_users'] ?> Active Users
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-user-shield"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Admin Users</span>
                        <span class="info-box-number"><?= number_format($stats['admin_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-user-tie"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Supervisor Users</span>
                        <span class="info-box-number"><?= number_format($stats['supervisor_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-user-friends"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Field Users</span>
                        <span class="info-box-number"><?= number_format($stats['field_users']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers and Farm Blocks Row -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Farmers</span>
                        <span class="info-box-number"><?= number_format($stats['total_farmers']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?= $stats['total_farmers'] > 0 ? ($stats['active_farmers'] / $stats['total_farmers']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_farmers'] ?> Active Farmers
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-map"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Crop Farm Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_crop_blocks']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-secondary elevation-1"><i class="fas fa-cow"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Livestock Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_livestock_blocks']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Data Row -->
        <div class="row">
            <!-- User Registration Trend Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">User Registration Trend (Last 6 Months)</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="registrationChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>

            <!-- User Distribution by Role -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">User Distribution by Role</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="roleChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users and District Coverage -->
        <div class="row">
            <!-- Recent Users -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Users</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recentUsers)): ?>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <tr>
                                            <td><?= esc($user['name']) ?></td>
                                            <td>
                                                <?php if ($user['is_admin'] == 1): ?>
                                                    <span class="badge badge-danger">Admin</span>
                                                <?php elseif ($user['is_supervisor'] == 1): ?>
                                                    <span class="badge badge-warning">Supervisor</span>
                                                <?php else: ?>
                                                    <span class="badge badge-info"><?= ucfirst($user['role']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['status'] == 1): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($user['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No users found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- District Coverage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">District Coverage</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>District</th>
                                    <th>Active Users</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($districtCoverage)): ?>
                                    <?php foreach ($districtCoverage as $district): ?>
                                        <tr>
                                            <td><?= esc($district['district_name']) ?></td>
                                            <td><span class="badge badge-primary"><?= $district['user_count'] ?></span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="2" class="text-center">No district coverage data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Permission Users -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Users with Most Permissions</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Permissions Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($topPermissionUsers)): ?>
                                    <?php foreach ($topPermissionUsers as $user): ?>
                                        <tr>
                                            <td><?= esc($user['name']) ?></td>
                                            <td>
                                                <?php if ($user['role'] == 'admin'): ?>
                                                    <span class="badge badge-danger">Admin</span>
                                                <?php elseif ($user['role'] == 'supervisor'): ?>
                                                    <span class="badge badge-warning">Supervisor</span>
                                                <?php else: ?>
                                                    <span class="badge badge-info"><?= ucfirst($user['role']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td><span class="badge badge-success"><?= $user['permission_count'] ?></span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">No permission data available</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-app">
                                    <i class="fas fa-user-plus"></i> Add User
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users') ?>" class="btn btn-app">
                                    <i class="fas fa-users"></i> Manage Users
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/reports') ?>" class="btn btn-app">
                                    <i class="fas fa-chart-bar"></i> View Reports
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/settings') ?>" class="btn btn-app">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Registration Trend Chart
    const registrationData = <?= json_encode($monthlyRegistrations) ?>;
    const registrationCtx = document.getElementById('registrationChart').getContext('2d');
    new Chart(registrationCtx, {
        type: 'line',
        data: {
            labels: registrationData.map(item => item.month),
            datasets: [{
                label: 'New Users',
                data: registrationData.map(item => item.count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Role Distribution Chart
    const roleData = <?= json_encode($usersByRole) ?>;
    const roleCtx = document.getElementById('roleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'doughnut',
        data: {
            labels: roleData.map(item => item.role.charAt(0).toUpperCase() + item.role.slice(1)),
            datasets: [{
                data: roleData.map(item => item.count),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>

<?= $this->endSection() ?>
