<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/field-visits') ?>" class="text-success">Field Visits</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Create</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Field Visits
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Create New Field Visit
                    </h5>
                    <p class="card-text mb-0 text-muted">Add a new field visit record</p>
                </div>
                <div class="card-body">
                    <!-- Error Messages -->
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Create Form -->
                    <form action="<?= base_url('staff/extension/field-visits') ?>" method="post">
                        <?= csrf_field() ?>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">Basic Information</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                <select class="form-select" id="llg_id" name="llg_id" required>
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>" <?= old('llg_id') == $llg['id'] ? 'selected' : '' ?>>
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="purpose" class="form-label">Purpose <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="purpose" name="purpose" 
                                       value="<?= old('purpose') ?>" required>
                                <div class="form-text">Brief description of the visit purpose</div>
                            </div>
                        </div>

                        <!-- Date Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">Date Information</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="date_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_start" name="date_start" 
                                       value="<?= old('date_start') ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="date_end" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date_end" name="date_end" 
                                       value="<?= old('date_end') ?>" required>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">Location Information</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="locations" class="form-label">Specific Locations</label>
                                <textarea class="form-control" id="locations" name="locations" rows="4"><?= old('locations') ?></textarea>
                                <div class="form-text">Enter specific locations visited (one per line)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="gps" class="form-label">GPS Coordinates</label>
                                <input type="text" class="form-control" id="gps" name="gps" 
                                       value="<?= old('gps') ?>" placeholder="-6.314993, 143.95555">
                                <div class="form-text">GPS coordinates for the main location</div>
                            </div>
                        </div>

                        <!-- Officers and Activities -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">Officers and Activities</h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="officers" class="form-label">Participating Officers</label>
                                <select class="form-select" id="officers" name="officers[]" multiple>
                                    <?php foreach ($officers as $officer): ?>
                                        <option value="<?= $officer['id'] ?>" 
                                                <?= in_array($officer['id'], old('officers', [])) ? 'selected' : '' ?>>
                                            <?= esc($officer['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Hold Ctrl/Cmd to select multiple officers</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="achievements" class="form-label">Achievements</label>
                                <textarea class="form-control" id="achievements" name="achievements" rows="4"><?= old('achievements') ?></textarea>
                                <div class="form-text">Key achievements and outcomes</div>
                            </div>
                        </div>

                        <!-- Beneficiaries -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="beneficiaries" class="form-label">Beneficiaries</label>
                                <textarea class="form-control" id="beneficiaries" name="beneficiaries" rows="4"><?= old('beneficiaries') ?></textarea>
                                <div class="form-text">Information about beneficiaries reached</div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12 d-flex justify-content-end">
                                <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-secondary me-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Create Field Visit
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Set minimum date for end date based on start date
        $('#date_start').on('change', function() {
            $('#date_end').attr('min', $(this).val());
        });

        // Initialize date validation
        const today = new Date().toISOString().split('T')[0];
        $('#date_start').attr('max', today);
        $('#date_end').attr('max', today);
    });
</script>
<?= $this->endSection() ?>
