<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?= $title ?></title>

  <link rel="shortcut icon" type="image/x-icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico">


  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <!--  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/fontawesome-free/css/all.min.css"> -->
  <!-- fullCalendar -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/fullcalendar/main.css">
  <!-- Toastr -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/dist/css/adminlte.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

  <!-- Select2 -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/select2/css/select2.min.css">
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">

  <!-- jQuery -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jquery/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <!-- Select2 -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/select2/js/select2.full.min.js"></script>

  <!-- Toastr -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/toastr/toastr.min.js"></script>
  <!-- jQuery UI -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jquery-ui/jquery-ui.min.js"></script>
  <!-- bs-custom-file-input -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
  <!-- AdminLTE App -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/dist/js/adminlte.min.js"></script>
  <!-- fullCalendar 2.2.5 -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/moment/moment.min.js"></script>

  <!-- DataTables  & Plugins -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables/jquery.dataTables.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/jszip/jszip.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/pdfmake/pdfmake.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/pdfmake/vfs_fonts.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.print.min.js"></script>
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

  <!-- OPTIONAL SCRIPTS -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/chart.js/Chart.min.js"></script>
  <!-- Include chartjs-plugin-labels plugin -->
  <!-- <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-labels"></script> -->

  <!-- AdminLTE for demo purposes -->
  <!-- <script src="<?= base_url() ?>/public/assets/themes/adminlte320/dist/js/demo.js"></script> -->
  <!-- Page specific script -->

  <script>
    $(function() {
      bsCustomFileInput.init();
    });
  </script>

</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">

    <?php if (session()->has('error')) : ?>
      <span class="toastrDefaultError"></span>
    <?php endif; ?>
    <?php if (session()->has('success')) : ?>
      <span class="toastrDefaultSuccess"></span>
    <?php endif; ?>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="<?= base_url() ?>admin/dashboard" class="nav-link text-success">
            <i class="fas fa-home"></i>
          </a>
        </li>

      </ul>

      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">


        <li class="nav-item">
          <a class="nav-link" data-widget="fullscreen" href="#" role="button">
            <i class="fas fa-expand-arrows-alt"></i>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-success" href="#" role="button">
            <i class="fas fa-question-circle"></i>
          </a>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-success elevation-4">
      <!-- Brand Logo -->
      <a href="#" class="brand-link bg-success">
        <img src="<?= imgcheck(session('orglogo')) ?>" alt="org logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light"></span>
        <small><?= session('orgcode') ?></small>
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- User Panel -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
          <div class="image">
            <img src="<?= base_url() ?>/public/assets/system_img/no-users-img.png" class="img-circle elevation-2" alt="User Image">
          </div>
          <div class="info">
            <a href="#" class="d-block"><?= session('name') ?></a>
          </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

          <!-- Admin Dashboard -->
          <li class="nav-item" style="background-color: rgba(40, 167, 69, 0.1); border-radius: 5px; margin: 5px 0;">
            <a href="<?= base_url() ?>admin/dashboard" class="nav-link <?= ($menu == "admin-dashboard") ? "active" : ""; ?>" style="border-left: 4px solid #28a745;">
              <i class="nav-icon fas fa-tachometer-alt text-success"></i>
              <p style="font-weight: 500;">Admin Dashboard</p>
            </a>
          </li>

          <!-- Staff Portal Access -->
          <li class="nav-item">
            <a href="<?= base_url() ?>staff" class="nav-link <?= ($menu == "staff") ? "active" : ""; ?>">
              <i class="nav-icon fas fa-user-tie text-success"></i>
              <p>Staff Portal</p>
            </a>
          </li>



            <!-- User Management -->
            <li class="nav-item <?= (in_array($menu, ['admin-users', 'admin-groups', 'admin-field-users'])) ? 'menu-open' : ''; ?>">
              <a href="#" class="nav-link <?= (in_array($menu, ['admin-users', 'admin-groups', 'admin-field-users'])) ? 'active' : ''; ?>">
                <i class="nav-icon fas fa-users text-success"></i>
                <p>
                  User Management
                  <i class="right fas fa-angle-left"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <?php $active = ($menu == "admin-users") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/users" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>Organization Users</p>
                  </a>
                </li>
                <li class="nav-item">
                  <?php $active = ($menu == "admin-groups") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/groups" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>User Groups</p>
                  </a>
                </li>
                <li class="nav-item">
                  <?php $active = ($menu == "admin-field-users") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/field-users" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>Field Users</p>
                  </a>
                </li>
              </ul>
            </li>















            



            <!-- Farmers, crop farm blocks, crops data, crops diseases, crops fertilizers, crops pesticides, crops harvests, crops markets -->
            
             
            
           
            <!-- Farmers -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-farmers") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/farmers" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-users text-success"></i>
                <p>Farmers</p>
              </a>
            </li>

            <!-- Buyers -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-buyers") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/buyers" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-handshake text-success"></i>
                <p>Buyers</p>
              </a>
            </li>

            <!-- Field Visits -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-field-visits") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/field-visits" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-map-marker-alt text-success"></i>
                <p>Field Visits</p>
              </a>
            </li>

            <!-- Trainings -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-trainings") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/trainings" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-chalkboard-teacher text-success"></i>
                <p>Trainings</p>
              </a>
            </li>

            <!-- Inputs -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-inputs") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/inputs" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-boxes text-success"></i>
                <p>Inputs</p>
              </a>
            </li>

            <!-- Crop Blocks -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crop-blocks") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crop-blocks" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-th text-success"></i>
                <p>Crop Blocks</p>
              </a>
            </li>

            <!-- Livestock Blocks -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-livestock-blocks") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/livestock-blocks" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-th-large text-success"></i>
                <p>Livestock Blocks</p>
              </a>
            </li>

            <!-- Crops Data -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-data") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-data" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-seedling text-success"></i>
                <p>Crops Data</p>
              </a>
            </li>

            <!-- Crops Diseases -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-diseases") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-diseases" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-bug text-success"></i>
                <p>Crops Diseases</p>
              </a>
            </li>

            <!-- Crops Fertilizers -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-fertilizers") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-fertilizers" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-tint text-success"></i>
                <p>Crops Fertilizers</p>
              </a>
            </li>

            <!-- Crops Pesticides -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-pesticides") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-pesticides" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-spray-can text-success"></i>
                <p>Crops Pesticides</p>
              </a>
            </li>

            <!-- Crops Harvests -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-harvests") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-harvests" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-tractor text-success"></i>
                <p>Crops Harvests</p>
              </a>
            </li>

            <!-- Crops Markets -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-crops-markets") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/crops-markets" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-shopping-cart text-success"></i>
                <p>Crops Markets</p>
              </a>
            </li>

            <!-- GPS Mapping -->
            <li class="nav-item">
              <?php $active = ($menu == "mapping") ? "active" : ""; ?>
              <a href="<?= base_url() ?>mapping" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-map-marked-alt text-success"></i>
                <p>GPS Mapping</p>
              </a>
            </li>





            <!-- Livestock -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-livestock") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/livestock" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-cow text-success"></i>
                <p>Livestock</p>
              </a>
            </li>

            <!-- Performance -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-performance") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/performance" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-chart-line text-success"></i>
                <p>Performance</p>
              </a>
            </li>

            <!-- Territory Coverage -->
            <li class="nav-item">
              <?php $active = ($menu == "admin-reports-territories") ? "active" : ""; ?>
              <a href="<?= base_url() ?>admin/reports/territories" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-map text-success"></i>
                <p>Territory Coverage</p>
              </a>
            </li>



            <!-- Settings -->
            <li class="nav-item">
              <?php $active = ($menu == "settings") ? "active" : ""; ?>
              <a href="<?= base_url() ?>settings" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-cog text-success"></i>
                <p>Settings</p>
              </a>
            </li>

            <!-- Admin Settings -->
            <li class="nav-item <?= (in_array($menu, ['admin-settings', 'admin-settings-general', 'admin-settings-data-validation', 'admin-settings-notifications'])) ? 'menu-open' : ''; ?>">
              <a href="#" class="nav-link <?= (in_array($menu, ['admin-settings', 'admin-settings-general', 'admin-settings-data-validation', 'admin-settings-notifications'])) ? 'active' : ''; ?>">
                <i class="nav-icon fas fa-cogs text-success"></i>
                <p>
                  Admin Settings
                  <i class="right fas fa-angle-left"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <?php $active = ($menu == "admin-settings") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/settings" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>Settings Dashboard</p>
                  </a>
                </li>
                <li class="nav-item">
                  <?php $active = ($menu == "admin-settings-general") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/settings/general" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>General Settings</p>
                  </a>
                </li>
                <li class="nav-item">
                  <?php $active = ($menu == "admin-settings-data-validation") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/settings/data-validation" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>Data Validation</p>
                  </a>
                </li>
                <li class="nav-item">
                  <?php $active = ($menu == "admin-settings-notifications") ? "active" : ""; ?>
                  <a href="<?= base_url() ?>admin/settings/notifications" class="nav-link <?= $active ?>">
                    <i class="far fa-circle nav-icon text-success"></i>
                    <p>Notifications</p>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Logout -->
            <li class="nav-item mt-4">
              <a href="<?= base_url() ?>logout" class="nav-link bg-danger">
                <i class="nav-icon fas fa-sign-out-alt"></i>
                <p>Log Out (<?= session('email') ? session('email') : session('name') ?>)</p>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <?= $this->renderSection('content') ?>
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
      <div class="row">
        <div class="col-md-6">
          <strong>Copyright &copy; <?= date('Y') ?> <a href="https://www.dakoiims.com" class="text-success">Dakoii Systems</a>.</strong>
          All rights reserved.
        </div>
        <div class="col-md-6 text-right">
          <div class="d-none d-sm-inline-block">
            <b class="text-success"><?= SYSTEM_NAME ?></b>
            <span class="text-muted">v<?= SYSTEM_VERSION ?></span>
          </div>
          <div class="mt-1">
            <small class="text-muted">
              Powered by <strong class="text-success">AgriStats</strong> |
              <a href="#" class="text-muted">Privacy Policy</a> |
              <a href="#" class="text-muted">Terms of Service</a>
            </small>
          </div>
        </div>
      </div>
    </footer>

    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->


  <?= $this->renderSection('calendar'); ?>

  <!-- Page-specific scripts -->
  <?= $this->renderSection('scripts') ?>
</body>

<script>
  $('.toastrDefaultSuccess').show(function() {
    toastr.success('<?= session('success') ?>')
    // toastr.success('Cook Liks')
  });
  $('.toastrDefaultError').show(function() {
    toastr.error('<?= session('error') ?>')
  });
</script>


</html>
