<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/farms') ?>">Farms</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Pesticides Data</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/farms/pesticides_data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Pesticides Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>Pesticides Data Records
                    </h5>
                    <p class="card-text mb-0 text-muted">Manage and monitor pesticide usage data across farm blocks</p>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="pesticidesDataTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Block Code</th>
                                    <th>Farmer</th>
                                    <th>Pesticide</th>
                                    <th>Crop</th>
                                    <th>Quantity</th>
                                    <th>Action Date</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($pesticides_data)): ?>
                                    <?php foreach ($pesticides_data as $pesticide): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($pesticide['block_code']) ?></strong>
                                                <br><small class="text-muted"><?= esc($pesticide['block_site']) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($pesticide['given_name'] . ' ' . $pesticide['surname']) ?></strong>
                                            </td>
                                            <td>
                                                <?php if (!empty($pesticide['pesticide_name'])): ?>
                                                    <span class="badge bg-info"><?= esc($pesticide['pesticide_name']) ?></span>
                                                    <br><small class="text-muted"><?= esc($pesticide['name']) ?></small>
                                                <?php else: ?>
                                                    <strong><?= esc($pesticide['name']) ?></strong>
                                                    <?php if (!empty($pesticide['brand'])): ?>
                                                        <br><small class="text-muted">Brand: <?= esc($pesticide['brand']) ?></small>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success" style="background-color: <?= esc($pesticide['crop_color_code'] ?? '#28a745') ?> !important;">
                                                    <?= esc($pesticide['crop_name']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?= esc($pesticide['quantity']) ?> <?= esc($pesticide['unit']) ?></strong>
                                                <br><small class="text-muted"><?= esc($pesticide['unit_of_measure']) ?></small>
                                            </td>
                                            <td>
                                                <?= date('M d, Y', strtotime($pesticide['action_date'])) ?>
                                                <br><small class="text-muted"><?= date('D', strtotime($pesticide['action_date'])) ?></small>
                                            </td>
                                            <td>
                                                <small>
                                                    <?= esc($pesticide['ward_name']) ?>, <?= esc($pesticide['llg_name']) ?>
                                                    <br><?= esc($pesticide['district_name']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($pesticide['status'] === 'active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?= ucfirst($pesticide['status']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/farms/pesticides_data/' . $pesticide['id']) ?>" 
                                                       class="btn btn-sm btn-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/farms/pesticides_data/' . $pesticide['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            title="Delete" 
                                                            onclick="confirmDelete(<?= $pesticide['id'] ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="post" style="display: none;">
    <?= csrf_field() ?>
</form>

<script>
$(document).ready(function() {
    // Initialize DataTable with proper empty state handling
    $('#pesticidesDataTable').DataTable({
        responsive: true,
        order: [[5, 'desc']], // Sort by action date descending
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on actions column
        ],
        language: {
            search: "Search pesticides data:",
            lengthMenu: "Show _MENU_ records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ pesticides records",
            infoEmpty: "No pesticides data found",
            infoFiltered: "(filtered from _MAX_ total records)",
            emptyTable: '<div class="text-center py-4"><i class="fas fa-bug fa-3x mb-3 text-muted"></i><p class="mb-2">No pesticides data found</p><small class="text-muted">Start by adding your first pesticide usage record</small><br><a href="<?= base_url('staff/farms/pesticides_data/create') ?>" class="btn btn-primary btn-sm mt-3"><i class="fas fa-plus me-1"></i>Add Pesticides Data</a></div>'
        }
    });
});

function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this pesticides data? This action cannot be undone.')) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('staff/farms/pesticides_data/') ?>' + id + '/delete';
        form.submit();
    }
}
</script>

<?= $this->endSection() ?> 