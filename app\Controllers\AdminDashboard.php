<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\PermissionsSetsModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\AdxDistrictModel;

class AdminDashboard extends BaseController
{
    protected $usersModel;
    protected $farmerModel;
    protected $cropBlockModel;
    protected $livestockBlockModel;
    protected $permissionsSetsModel;
    protected $permissionsUserDistrictsModel;
    protected $districtModel;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropBlockModel = new CropsFarmBlockModel();
        $this->livestockBlockModel = new LivestockFarmBlockModel();
        $this->permissionsSetsModel = new PermissionsSetsModel();
        $this->permissionsUserDistrictsModel = new PermissionsUserDistrictsModel();
        $this->districtModel = new AdxDistrictModel();
        helper(['form', 'url', 'info']);
    }

    /**
     * Admin Dashboard
     */
    public function index()
    {
        $orgId = session()->get('org_id');
        $provinceId = session()->get('orgprovince_id');

        // Check if org_id is available in session
        if (empty($orgId)) {
            return redirect()->to('admin/login')->with('error', 'Session expired. Please login again.');
        }

        // Get organization statistics
        $stats = [
            'total_users' => $this->usersModel->where('org_id', $orgId)->countAllResults(),
            'active_users' => $this->usersModel->where('org_id', $orgId)->where('status', 1)->countAllResults(),
            'admin_users' => $this->usersModel->where('org_id', $orgId)->where('is_admin', 1)->countAllResults(),
            'supervisor_users' => $this->usersModel->where('org_id', $orgId)->where('is_supervisor', 1)->countAllResults(),
            'field_users' => $this->usersModel->where('org_id', $orgId)->where('role', 'user')->countAllResults(),
            'total_farmers' => $this->farmerModel->where('org_id', $orgId)->countAllResults(),
            'active_farmers' => $this->farmerModel->where('org_id', $orgId)->where('status', 'active')->countAllResults(),
            'total_crop_blocks' => $this->cropBlockModel->where('org_id', $orgId)->countAllResults(),
            'total_livestock_blocks' => $this->livestockBlockModel->where('org_id', $orgId)->countAllResults()
        ];

        // Get recent users (last 10)
        $recentUsers = $this->usersModel->where('org_id', $orgId)
                                       ->orderBy('created_at', 'DESC')
                                       ->limit(10)
                                       ->findAll();

        // Get user distribution by role
        $usersByRole = $this->usersModel->select('role, COUNT(*) as count')
                                       ->where('org_id', $orgId)
                                       ->groupBy('role')
                                       ->findAll();

        // Get district coverage
        $districtCoverage = $this->permissionsUserDistrictsModel
                                ->select('adx_district.name as district_name, COUNT(DISTINCT permissions_user_districts.user_id) as user_count')
                                ->join('adx_district', 'adx_district.id = permissions_user_districts.district_id')
                                ->join('users', 'users.id = permissions_user_districts.user_id')
                                ->where('permissions_user_districts.org_id', $orgId)
                                ->where('users.status', 1)
                                ->groupBy('permissions_user_districts.district_id')
                                ->orderBy('user_count', 'DESC')
                                ->findAll();

        // Get users with most permissions - temporarily removing org_id filter
        $topPermissionUsers = $this->permissionsSetsModel
                                  ->select('users.name, users.role, COUNT(permissions_sets.permission_id) as permission_count')
                                  ->join('users', 'users.id = permissions_sets.user_id')
                                  ->where('users.status', 1)
                                  ->groupBy('permissions_sets.user_id')
                                  ->orderBy('permission_count', 'DESC')
                                  ->limit(10)
                                  ->findAll();

        // Get monthly user registration trend (last 6 months) - users table doesn't have org_id
        $monthlyRegistrations = [];
        for ($i = 5; $i >= 0; $i--) {
            $startDate = date('Y-m-01', strtotime("-$i months"));
            $endDate = date('Y-m-t', strtotime("-$i months"));

            $count = $this->usersModel->where('created_at >=', $startDate)
                                     ->where('created_at <=', $endDate . ' 23:59:59')
                                     ->countAllResults();
            $monthlyRegistrations[] = [
                'month' => date('M Y', strtotime("-$i months")),
                'count' => $count
            ];
        }

        $data = [
            'title' => 'Admin Dashboard',
            'page_header' => 'Admin Dashboard',
            'page_desc' => 'Organization Management Overview',
            'menu' => 'admin-dashboard',
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'usersByRole' => $usersByRole,
            'districtCoverage' => $districtCoverage,
            'topPermissionUsers' => $topPermissionUsers,
            'monthlyRegistrations' => $monthlyRegistrations
        ];

        return view('admin_dashboard/admin_dashboard_index', $data);
    }


}
