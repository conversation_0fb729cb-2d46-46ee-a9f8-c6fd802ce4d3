<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff') ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('staff/farms/fertilizer_data') ?>">Fertilizer Data</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($block['block_code']) ?></li>
                        </ol>
                    </nav>
                </div>
                <!-- Right side: Action buttons -->
                <div>
                    <a href="<?= base_url('staff/farms/fertilizer_data') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Fertilizer Data
                    </a>
                    <a href="<?= base_url('staff/farms/fertilizer_data/create/' . $block['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Fertilizer Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Block Information Card -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Farm Block Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Block Code:</strong><br>
                            <span class="text-primary"><?= esc($block['block_code']) ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Farmer:</strong><br>
                            <?= esc($block['given_name']) ?> <?= esc($block['surname']) ?><br>
                            <small class="text-muted">Code: <?= esc($block['farmer_code']) ?></small>
                        </div>
                        <div class="col-md-3">
                            <strong>Crop:</strong><br>
                            <span class="badge bg-success"><?= esc($block['crop_name']) ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Location:</strong><br>
                            <?= esc($block['district_name']) ?><br>
                            <small class="text-muted"><?= esc($block['llg_name']) ?> / <?= esc($block['ward_name']) ?></small>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>Block Site:</strong><br>
                            <?= esc($block['block_site']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Village:</strong><br>
                            <?= esc($block['village']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fertilizer Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-flask me-2"></i>Fertilizer Application History
                    </h5>
                    <p class="card-text mb-0 text-muted">
                        Complete history of fertilizer applications for this farm block
                    </p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Fertilizer Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="fertilizerApplicationsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Fertilizer Type</th>
                                    <th>Name</th>
                                    <th>Brand</th>
                                    <th>Unit</th>
                                    <th>UoM</th>
                                    <th>Quantity</th>
                                    <th>Applied By</th>
                                    <th>Remarks</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($fertilizer_data)): ?>
                                    <?php foreach ($fertilizer_data as $data): ?>
                                        <tr>
                                            <td>
                                                <strong><?= date('M d, Y', strtotime($data['action_date'])) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($data['fertilizer_name']) ?></span>
                                            </td>
                                            <td><?= esc($data['name']) ?></td>
                                            <td><?= esc($data['brand']) ?></td>
                                            <td><?= esc($data['unit']) ?></td>
                                            <td><?= esc($data['unit_of_measure']) ?></td>
                                            <td>
                                                <strong><?= esc($data['quantity']) ?></strong>
                                            </td>
                                            <td>
                                                <?= esc($data['created_by_name'] ?? 'Unknown') ?>
                                            </td>
                                            <td>
                                                <?= esc($data['remarks']) ?: '-' ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/farms/fertilizer_data/edit/' . $data['id']) ?>"
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/farms/fertilizer_data/delete/' . $data['id']) ?>"
                                                       class="btn btn-sm btn-danger" title="Delete"
                                                       onclick="return confirm('Are you sure you want to delete this fertilizer data?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <?php if (empty($fertilizer_data)): ?>
                            <tfoot>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No fertilizer data found for this farm block.
                                        <br>
                                        <a href="<?= base_url('staff/farms/fertilizer_data/create/' . $block['id']) ?>"
                                           class="btn btn-primary btn-sm mt-2">
                                            <i class="fas fa-plus me-1"></i>Add First Fertilizer Data
                                        </a>
                                    </td>
                                </tr>
                            </tfoot>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Prevent template JS from interfering with form submissions
    $('form').addClass('fertilizer-form');

    // Wait for all scripts to load and DOM to be fully ready
    setTimeout(function() {
        try {
            var tableId = '#fertilizerApplicationsTable';
            var $table = $(tableId);

            // Check if table exists and has proper structure
            if ($table.length && $table.find('thead th').length > 0) {
                // Count header columns
                var headerColumnCount = $table.find('thead th').length;
                console.log('Header columns:', headerColumnCount);

                // Check if table has data rows or empty state
                var $tbody = $table.find('tbody');
                var hasData = $tbody.find('tr').length > 0;
                console.log('Has data rows:', hasData);

                // If table is empty, add a proper empty row to match column count
                if (!hasData || $tbody.find('tr td[colspan]').length > 0) {
                    console.log('Table is empty, ensuring proper structure');
                    // Table is empty or has colspan row, ensure it's properly structured
                    var emptyRow = $tbody.find('tr td[colspan]').parent();
                    if (emptyRow.length > 0) {
                        // Verify colspan matches header count
                        var colspanValue = emptyRow.find('td').attr('colspan');
                        if (parseInt(colspanValue) !== headerColumnCount) {
                            console.warn('Colspan mismatch: ' + colspanValue + ' vs ' + headerColumnCount);
                            emptyRow.find('td').attr('colspan', headerColumnCount);
                        }
                    }
                }

                // Destroy any existing DataTable instance
                if ($.fn.DataTable.isDataTable(tableId)) {
                    $table.DataTable().destroy();
                }

                // Initialize DataTable with error handling
                var dataTable = $table.DataTable({
                    responsive: true,
                    order: hasData ? [[0, 'desc']] : [], // Only set order if there's data
                    pageLength: 25,
                    columnDefs: [
                        { orderable: false, targets: -1 } // Disable sorting on Actions column
                    ],
                    language: {
                        search: "Search Fertilizer Applications:",
                        lengthMenu: "Show _MENU_ records per page",
                        info: "Showing _START_ to _END_ of _TOTAL_ records",
                        infoEmpty: "No fertilizer applications found",
                        infoFiltered: "(filtered from _MAX_ total records)",
                        emptyTable: "No fertilizer applications found for this farm block"
                    },
                    // Handle empty table gracefully
                    initComplete: function() {
                        console.log('Fertilizer Applications DataTable initialized successfully');
                    },
                    // Additional options for better empty table handling
                    deferRender: true,
                    processing: false
                });

                // Handle DataTable errors
                dataTable.on('error.dt', function(e, settings, techNote, message) {
                    console.error('DataTable error:', message);
                    console.error('Technical note:', techNote);
                });

            } else {
                console.warn('Fertilizer Applications table not found or has no headers');
            }
        } catch (error) {
            console.error('Error initializing Fertilizer Applications DataTable:', error);
            console.error('Error stack:', error.stack);
        }
    }, 300); // Increased delay further to ensure all template scripts are loaded
});
</script>
<?= $this->endSection() ?>
