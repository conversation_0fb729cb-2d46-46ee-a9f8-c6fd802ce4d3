<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= $page_header ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Field Visits</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/extension/field-visits/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Field Visit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>Field Visits
                    </h5>
                    <p class="card-text mb-0 text-muted">Manage and track field visits and extension activities</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Field Visits Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="fieldVisitsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Location</th>
                                    <th>Date Range</th>
                                    <th>Purpose</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($visits)): ?>
                                    <?php foreach ($visits as $visit): ?>
                                        <tr>
                                            <td><strong><?= $visit['id'] ?></strong></td>
                                            <td>
                                                <strong><?= esc($visit['llg_name'] ?? 'N/A') ?></strong>
                                                <?php
                                                // Display specific locations from the locations field
                                                $locations = [];
                                                if (!empty($visit['locations'])) {
                                                    // Handle both string and already decoded JSON
                                                    if (is_string($visit['locations'])) {
                                                        $locationsData = json_decode($visit['locations'], true);
                                                    } else {
                                                        $locationsData = $visit['locations'];
                                                    }
                                                    
                                                    if (is_array($locationsData)) {
                                                        $locations = array_slice($locationsData, 0, 2); // Show first 2 locations
                                                    }
                                                }
                                                ?>
                                                <?php if (!empty($locations)): ?>
                                                    <br><small class="text-muted">
                                                        <?= implode(', ', array_map('esc', $locations)) ?>
                                                        <?php if (count($locationsData ?? []) > 2): ?>
                                                            <span class="badge bg-info">+<?= count($locationsData) - 2 ?> more</span>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= date('d M Y', strtotime($visit['date_start'])) ?></strong><br>
                                                <small class="text-muted">to <?= date('d M Y', strtotime($visit['date_end'])) ?></small>
                                            </td>
                                            <td>
                                                <?= esc(substr($visit['purpose'], 0, 50)) ?>
                                                <?php if (strlen($visit['purpose']) > 50): ?>
                                                    <span class="text-muted">...</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($visit['status'] == 1): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d M Y', strtotime($visit['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/extension/field-visits/' . $visit['id']) ?>" 
                                                       class="btn btn-sm btn-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/extension/field-visits/' . $visit['id'] . '/edit') ?>" 
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="post" action="<?= base_url('staff/extension/field-visits/' . $visit['id'] . '/delete') ?>" 
                                                          style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this field visit?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>No field visits found
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#fieldVisitsTable').DataTable({
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 25,
            columnDefs: [
                { orderable: false, targets: 6 } // Actions column
            ],
            language: {
                search: "Search Field Visits:",
                lengthMenu: "Show _MENU_ field visits per page",
                info: "Showing _START_ to _END_ of _TOTAL_ field visits",
                infoEmpty: "No field visits found",
                infoFiltered: "(filtered from _MAX_ total field visits)"
            }
        });
    });
</script>
<?= $this->endSection() ?>
